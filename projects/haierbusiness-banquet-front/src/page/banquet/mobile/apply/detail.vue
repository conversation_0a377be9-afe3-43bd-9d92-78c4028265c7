<template>
  <div class="apply-detail">
    <!-- <van-nav-bar  class="top-bg-color" :fixed="true"  title="申请单详情" left-arrow>
      <template #left>
        <van-icon name="arrow-left" color="#000" @click="goBack" />
      </template>
    </van-nav-bar> -->

    <!-- 基本信息 -->
    <van-cell-group inset style="background-color: rgba(0,0,0,0);" class="mb-10">
      <van-cell title="申请单编号" value-class="large-value"  :value="detail?.orderCode" class="order-title-cell" />
      <van-cell title="经办人" :value="`${detail?.name}(${detail?.code})`" />
      <van-cell title="联系电话" :value="detail?.phone" />
      <van-cell title="订单类型" :value="detail?.sceneType == 1 ? '宴请' : '外卖'" />
      <van-cell title="申请时间" :value="detail?.applicationTime" />
      <van-cell title="签单人信息" :value="`${detail?.signerName}(${detail?.signerCode})`" />
      <van-cell v-if="policies?.restrictedCity && policies?.restrictedCity?.length > 0" title="就餐城市" :value="detail?.mealLocationCity" />
      <van-cell v-if="policies?.advanceChooseRestaurant || detail?.restaurantName" title="餐厅名称" value-class="large-value" :value="detail?.restaurantName" />
      <van-cell v-if="policies?.advanceChooseRestaurant || detail?.restaurantName" title="餐厅地址" value-class="large-value" :value="detail?.restaurantAddress" />
      <van-cell title="预计就餐时间" value-class="large-value">
        <template #value>
          <div class="font-size-10" v-if="detail?.estimatedMealTimeStart && detail?.estimatedMealTimeEnd"> {{ detail?.estimatedMealTimeStart.substring(0, 10) }} 至 {{ detail?.estimatedMealTimeEnd.substring(0, 10) }}</div>
          <div v-if="detail?.estimatedMealTimeEnd" style="color: #FF5533; font-size: 12px;">结算截止时间为:{{ dayjs(detail?.estimatedMealTimeEnd).add(1, 'day').format('YYYY-MM-DD') }} 06:00</div>
        </template>
        
      </van-cell>
      <van-cell title="申请事由" :value="detail?.banquetReason" />
      <van-cell title="附件信息" @click="showPreview(detail?.fileUrl)" :value="detail?.fileName" />
      <van-cell title="备注信息" :value="detail?.remark" />

    </van-cell-group>


    <!-- 预算信息 -->
    <van-cell-group inset class="mb-10">
      <van-cell title="预算申请金额" :value="`${detail?.budgetAmount}元`" />
      <van-cell title="实际消费金额" :value="`${detail?.actualPaymentAmount}元`" />
      <van-cell title="预算剩余金额" :value="`${ mathNumber(detail?.budgetIssuance, detail?.actualPaymentAmount) }元`" />
      <van-cell title="释放金额" :value="`${ mathNumber(item?.budgetIssuance, item?.actualPaymentAmount) }元`"/>
      <van-cell title="预算流水单号" value-class="large-value" :value="detail?.payCode" />
      <template v-if="showMoneyMore">
        <van-cell title="预算系统" :value="detail?.budgetSysCode" />
        <van-cell title="费用科目" :value="detail?.feeItemName" />
        <van-cell title="预算部门" :value="detail?.budgetDepartmentName" />

      </template>
      <van-cell @click="showMoneyMore = !showMoneyMore">
        <template #value>
          <div class="flex align-items-center justify-content-center ">
            <div class="mr-5" >{{showMoneyMore ? '收起' : '查看更多'}}</div>
            <van-icon :name="!showMoneyMore ? 'arrow-down' : 'arrow-up'" />
          </div>
        </template>
      </van-cell>

    </van-cell-group>
    <!-- 就餐人信息 -->
    <!-- <van-cell-group inset class="mb-10">
      <van-cell title="就餐人信息" :value="`${detail?.persons?.length}人`" />
      <template v-if="showPersonMore">
        <van-cell title="内部就餐人"  >
          <template #value>
            <div>
              <div v-for="item, index in detail?.persons" :key="index">
                <span v-if="item.haierUser == true">{{ `${item.userName}(${item.userCode})` }}</span>
              </div>
            </div>
          </template>
        </van-cell>
        <van-cell title="外部就餐人" >
          <template #value>
            {{ detail?.persons?.filter(item => item.haierUser != true)?.map(item2=> item2.userName)?.join(',') }}
          </template>
        </van-cell>

      </template>
      <van-cell @click="showPersonMore = !showPersonMore">
        <template #value>
          <div class="flex align-items-center justify-content-center ">
            <div class="mr-5" >{{showPersonMore ? '收起明细' : '查看明细'}}</div>
            <van-icon :name="!showPersonMore ? 'arrow-down' : 'arrow-up'" />
          </div>
        </template>
      </van-cell>

    </van-cell-group> -->

    <van-cell-group inset class="mb-10" v-for="item,index in detail?.bookings" :key="index">
      <van-cell title="预订单编号" value-class="large-value" :value="item?.orderBookingCode" />
      <van-cell title="就餐时间" :value="item?.mealTime" />
      <van-cell title="就餐金额" value-class="large-value">
        <template #value>
          <div>{{ item?.actualPaymentAmount }}</div>
          <div>{{ `(企业:${item?.entPayAmount} 元;个人:${item?.staffPayAmount} 元)` }}</div>
        </template> 
      </van-cell>
      <van-cell title="就餐餐厅" :value="item?.restaurantName" />
      <!-- <van-cell title="就餐人数" :value="detail?.persons?.length" /> -->
      <!-- item?.waterTicketInformation -->
      <van-cell v-if="detail?.sceneType == 1" title="水票附件查看" @click="showPreview(item?.waterTicketInformation)" value="查看水票" />

    </van-cell-group>


    <van-cell-group inset class="mb-10"  v-for="item,index in detail?.refunds" :key="index">
      <van-cell title="退款单编号" value-class="large-value" :value="item?.refundOrderCode" />
      <van-cell title="处理时间" :value="item?.createTime" />
      <van-cell title="退款金额" value-class="large-value">
        <template #value>
          <div>{{ item?.refundAmount }}</div>
          <div>{{ `(企业:${item?.entRefundAmount || 0}元;个人:${item?.staffRefundAmount || 0}元)` }}</div>
        </template> 
      </van-cell>
      <van-cell title="退款餐厅" :value="item?.restaurantName" />
      <van-cell title="签单人" :value="item?.signerName" />

    </van-cell-group>
   
    <van-row @click="goToApproval(detail?.processCode)" style="height: 40px; color: rgb(36 111 255);" justify="center"
      align-itemts="center">订单审批记录</van-row>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import type { Ref } from 'vue';

import { banquetApi } from '@haierbusiness-front/apis';
import { showImagePreview } from 'vant';

import {
  RHotelParams,
  RpayType,
  ROrderPayMentStateEnum,
  ROrderPayMentStateEnumColor,
  ROrderApprovalStateEnum,
  ROrderApprovalStateEnumColor,
  ROrderStateEnum,
  ROrderStateEnumColor,
  BApplyListRecord,
  BPoliciesRes
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { Item } from 'ant-design-vue/es/menu';
import { showSuccessToast, showFailToast } from 'vant';
import dayjs from 'dayjs';

const router = getCurrentRouter();

const route = ref(getCurrentRoute());

const id = route.value?.query?.id;

const getInnerPerson = (arr:any) => {
  let innerPersonList = arr?.filter(item => item.haierUser == true)
  return innerPersonList?.map(item2=> `${item2.userName}(${item2.userCode})`)?.join(',')
}

const businessList = import.meta.env.VITE_BUSINESS_URL;
const store = applicationStore();

const { loginUser } = storeToRefs(store);

const detail = ref<BApplyListRecord>();


const mathNumber = (num1: number, num2: number) => {
  if (!num1 && !num2) {
    return 0
  }
  return numberCalculate(num1, num2, '-')
}

const showPreview =(url:string) => {
  // pdf
  if(url.indexOf('.pdf')> 0) {
    location.href = url
    return
  }
  showImagePreview({
    images: [url],
    startPosition: 0,
  });
}

/** 封装一个公共方法numberCalculate() 用于浮点数运算 */
//num1 num2传入两个值 symbol +-*/符号
const numberCalculate = (num1: number, num2: number, symbol: string) => {
    var str1 = num1.toString(), str2 = num2.toString(), result, str1Length, str2Length
    try {
        //获取小数点后的精度
        str1Length = str1.split('.')[1].length 
    } 
    catch (error) { 
        //解决整数没有小数点方法
        str1Length = 0 
    }
    try { 
        str2Length = str2.split('.')[1].length 
    } catch (error) { 
        str2Length = 0 
    }
    // 取两个数的最小精度，即小数点后数字的最大长度
    var maxLen = Math.max(str1Length, str2Length)
    // step将两个数都转化为整数至少小数点后移多少位
    var step = Math.pow(10, maxLen)

    switch (symbol) {
        case "+":
        // toFixed()根据最小精度截取运算结果
        result = ((num1 * step + num2 * step) / step).toFixed(maxLen)
        break;
        case "-":
        result = ((num1 * step - num2 * step) / step).toFixed(maxLen)
        break;
        case "*":
        result = (((num1 * step) * (num2 * step)) / step / step).toFixed(maxLen)
        break;
        case "/":
        result = ((num1 * step) / (num2 * step)).toFixed(maxLen)
        break;
        default:
        break;
    }
    // 由于toFixed方法返回结果是字符串，还需要转回number输出
    return Number(result)
}


const getDetail = (id: string) => {
  const params = {
    id,
  };

  banquetApi.getApplyDetail(params).then((res) => {
    detail.value = res;
    detail.value.name = loginUser.value?.nickName
    detail.value.code = loginUser.value?.username 
    detail.value.phone= loginUser.value?.phone //联系电话
    // detail.value.estimatedMealTimeEnd = detail.value.estimatedMealTimeEnd && detail.value.estimatedMealTimeEnd.length >= 18 ? detail.value.estimatedMealTimeEnd.substring(0, 10) : detail.value.estimatedMealTimeEnd
    // detail.value.estimatedMealTimeStart = detail.value.estimatedMealTimeStart && detail.value.estimatedMealTimeStart.length >= 18  ? detail.value.estimatedMealTimeStart.substring(0, 10) : detail.value.estimatedMealTimeStart


    // 根据餐厅id 获取餐厅地址
    if (detail.value.restaurantId) {
      banquetApi.getRestaurant({
        restaurantId: detail.value.restaurantId
      }).then(res2 => {
        detail.value.restaurantAddress = res2.restaurantAddress
      })
    }

    
  });

  
};

// 获取当前用户受管控信息
const policies = ref<BPoliciesRes>()

const getPolicies = () => {
  banquetApi.getPolicies().then(res => {
    policies.value = res
  })
}

onMounted(() => {
  getPolicies()
})

// 展开预算相关信息
const showMoneyMore = ref<boolean>(false);

// 展开就餐人详情
const showPersonMore = ref<boolean>(false);
const goBack = () => {
  router.back(-1);
};

const goToApproval = (processCode?: number | string) => {
  if (!processCode) {
    showFailToast('暂无审批记录!');
    return 
  }
  window.location.href = `${businessList}hbweb/process/?code=${processCode}#/details`
  // https://travel-test.haier.net/hbweb/process/?code=PRO20241008154217330548#/details
  // router.push({ path: '/banquet/apply/approval', query: { processCode: processCode } });
};

watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);
</script>

<style lang="less" scoped>
@import url(../common.less);

.apply-detail {
  padding-top: 20px;
  background-color: #FAFBFD;
}
:deep(.large-value) {
  min-width: 70%;
}

:deep(.van-cell-group--inset) {
  box-shadow: 0px 8px 8px 0px rgba(157,178,232,0.1);
}
</style>