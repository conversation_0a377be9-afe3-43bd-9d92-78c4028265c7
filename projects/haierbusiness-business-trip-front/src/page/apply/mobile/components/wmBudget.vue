<script setup lang="ts">
import {
  showFailToast,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  Form as <PERSON><PERSON><PERSON>,
  Field as <PERSON><PERSON><PERSON>,
  CellGroup as VanCellGroup,
  showDialog,
  showSuccessToast,
  NoticeBar,
  Popup as VanPopup,
  showToast
} from "vant";
// import "vant/es/notice-bar/style";
import {
  payApi,
  compositionPayApi,
  virtualPayApi,
  budgetHaierPayApi,
  organizationCenterApi
} from "@haierbusiness-front/apis";
import {
  IPayData,
  IQueryVirtualAccountsResponse,
  PaySourceConstant,
  HaierBudgetSourceConstant,
  IloginUser,
  IBudgetHaierTypesResponse
} from "@haierbusiness-front/common-libs";
import { computed, PropType, ref, watch, onMounted } from "vue";
import {
  removeStorageItem,
  ITraveler,
  isMobile
} from "@haierbusiness-front/utils";
import { useRequest } from "vue-request";
import userSelectM from "./userSelectM.vue";
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import { showLoadingToast, closeToast } from 'vant';

import dayjs from 'dayjs';

interface Props {
  applicationCode: any;
  budgetType: string;
  param?: IPayData;
  haierBudgetPayOccupyRequest: any;
  source?: string;
}

const props = withDefaults(defineProps<Props>(), {});

const budgetBelong = ref(0);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const budgeterName = ref<any>()
const budgeterCode = ref<any>()

const budgetMonth = ref()
budgetMonth.value = dayjs().format('YYYY-MM');

const emit = defineEmits(["setIsPayComplete", "payComplete", "choosedBudget"]);

const budgetGroupName = ref<any>()
const budgetGroupCode = ref<any>()

const leftAmt = ref<any>()

const confirmLoading = ref(false);

// 选择预算人
const selectBudgeter = (item: ITraveler) => {
  console.log(item)
  budgeterCode.value = item.username;
  budgeterName.value = item.nickName;
  getBudget()
};

const getBudget = () => {
  const toast = showLoadingToast({
    duration: 0,
    forbidClick: true,
  });

  budgetHaierPayApi
    .searchHBC2Budget({
      applicationCode: 'haierbusiness-business-trip',
      budgeterCode: budgeterCode.value,
      haierBudgetType: props.budgetType,
      businessType: 'SLCL',
      budgetMonth: dayjs(budgetMonth.value).month() + 1
    })
    .then((res) => {
      closeToast();
      budgetGroupName.value = res[0].budgetDepartmentName
      budgetGroupCode.value = res[0].budgetDepartmentCode
      leftAmt.value = res[0].leftAmt

    })
    .finally(() => {
      // closeToast();

    })
    .catch(() => {
      // closeToast();
      budgetGroupName.value = ""
      budgetGroupCode.value = ""
      leftAmt.value = ""
    })
}

defineExpose({});

const onSubmit = () => {
  const params = {
    haierBudgetType: 'DEPT_WM',
    budgeterCode: budgeterCode.value,
    budgeterName: budgeterName.value,
    budgetDepartmentCode: budgetGroupCode.value,
    budgetDepartmentName: budgetGroupName.value,
    budgetMonth: dayjs(budgetMonth.value).month() + 1,
    businessType: "SLCL",
    budgetAmount: leftAmt.value,
    paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
    leftAmt: leftAmt.value,

  }
  emit('choosedBudget', params);
}

onMounted(() => {
  if (props.haierBudgetPayOccupyRequest?.haierBudgetType && props.haierBudgetPayOccupyRequest?.haierBudgetType == "DEPT_WM") {
    budgeterName.value = props.haierBudgetPayOccupyRequest.budgeterName
    budgeterCode.value = props.haierBudgetPayOccupyRequest.budgeterCode
    getBudget()
  }
})

</script>

<template>
  <div class="contentBox">
    <van-form @submit="onSubmit">

      <van-field v-if="props.source == 'HWORK'" v-model="budgeterName" input-align="right" :disabled="true" name="预算信息"
        label="预算信息" />

      <userSelectM v-else :source="props.source" :code="loginUser?.username" label="预算信息" type="hbc2"
        :value="budgeterName" @chose="selectBudgeter" />


      <van-field v-model="budgetMonth" input-align="right" :disabled="true" name="预算月份" label="预算月份"
        :rules="[{ required: true, message: '请填写预算月份' }]" />

      <van-field v-model="budgetGroupName" input-align="right" :disabled="true" name="部门名称" label="部门名称"
        :rules="[{ required: true, message: '请填写部门名称' }]" />

      <van-field v-model="leftAmt" input-align="right" :disabled="true" name="预算金额" label="预算金额"
        :rules="[{ required: true, message: '请填写预算金额' }]" />


      <div style="margin: 16px;">
        <van-button round block type="primary" native-type="submit">
          确定
        </van-button>
      </div>

    </van-form>
  </div>
</template>

<style scoped lang="less">
:deep(.van-tabs__line) {
  width: 125px;
  height: 4px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.1) 100%), #2681FF;
}

:deep(.van-tabs__content) {
  padding-top: 10px;
}

:deep(.czfr) {
  flex: none;
}
</style>

<style>
:root:root {
  --van-button-primary-background: #0073e5;
  --van-radio-checked-icon-color: #0073e5;
  --van-password-input-background: #f2f2f2;
}
</style>