<script setup lang="ts">
import {
  showF<PERSON>Toast,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  Form as <PERSON><PERSON><PERSON>,
  Field as <PERSON><PERSON><PERSON>,
  CellGroup as VanCellGroup,
  showDialog,
  showSuccessToast,
  NoticeBar,
  Popup as VanPopup,
  showToast
} from "vant";
// import "vant/es/notice-bar/style";
import {
  payApi,
  compositionPayApi,
  virtualPayApi,
  budgetHaierPayApi,
  organizationCenterApi
} from "@haierbusiness-front/apis";
import {
  IPayData,
  IQueryVirtualAccountsResponse,
  PaySourceConstant,
  HaierBudgetSourceConstant,
  IloginUser,
  IBudgetHaierTypesResponse
} from "@haierbusiness-front/common-libs";
import { computed, PropType, ref, watch,onMounted } from "vue";
import {
  removeStorageItem,
  ITraveler,
  isMobile
} from "@haierbusiness-front/utils";
import { useRequest } from "vue-request";
import userSelectM from "@/components/userSelectM.vue";
import deptSelectM from "@/components/deptSelectM.vue";
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';


interface Props {
  applicationCode: any;
  budgetType: string;
  param?: IPayData;
  haierBudgetPayOccupyRequest: any;
  source?: string;
}

const props = withDefaults(defineProps<Props>(), {});
const budgetBelong = ref(0);

// watch(budgetBelong, (New, Old) => {
//   console.log(`新值:${New} ——— 老值:${Old}`)
//   clear()
//   budgeterName.value = null
// })

const changeBelong = () => {
  clear()
  budgeterName.value = null
}

const store = applicationStore();
const { loginUser } = storeToRefs(store);
onMounted(() => {
  if(props.haierBudgetPayOccupyRequest?.haierBudgetType && props.haierBudgetPayOccupyRequest?.haierBudgetType=="DEPT_HBC_2" && (props.haierBudgetPayOccupyRequest?.budgeterCode || props.haierBudgetPayOccupyRequest?.budgetDepartmentName)){
      budgetBelong.value = props.haierBudgetPayOccupyRequest.isQueryDept

      if(budgetBelong.value == 0) {
          // userName.value = props.haierBudgetPayOccupyRequest.budgeterCode
          // nickName.value = props.haierBudgetPayOccupyRequest.budgeterName
          budgeterCode.value = props.haierBudgetPayOccupyRequest.budgeterCode
          budgeterName.value = props.haierBudgetPayOccupyRequest.budgeterName
          // 个人预算 
          userSelectedRowRecord.value = {
            budgetGroupCode:  props.haierBudgetPayOccupyRequest.budgetDepartmentCode,
            budgetGroupName:  props.haierBudgetPayOccupyRequest.budgetDepartmentName,
            performGroupCode: props.haierBudgetPayOccupyRequest.performCode,
            performGroupName: props.haierBudgetPayOccupyRequest.performName,
            financialCode:  props.haierBudgetPayOccupyRequest.financialCode,
            financialName:  props.haierBudgetPayOccupyRequest.financialName,
            budgetMasterCode: props.haierBudgetPayOccupyRequest.budgetManager,
            budgetMasterName: props.haierBudgetPayOccupyRequest.budgetManager,
            systemCode: props.haierBudgetPayOccupyRequest.orgSource,
            id: 0
          }

          selectMainInfo(userSelectedRowRecord.value)
      }else {
        financialName.value = props.haierBudgetPayOccupyRequest.budgetDepartmentName
        // 部门预算
        deptSelectedRowRecord.value = {
          budgetCode: props.haierBudgetPayOccupyRequest.budgetDepartmentCode,
          performCode: props.haierBudgetPayOccupyRequest.performCode,
          financialCode:  props.haierBudgetPayOccupyRequest.financialCode,
          financialName:  props.haierBudgetPayOccupyRequest.budgetDepartmentName,
          masterCode: props.haierBudgetPayOccupyRequest.budgetManager,
          masterName: props.haierBudgetPayOccupyRequest.budgetManager,
          systemCode: props.haierBudgetPayOccupyRequest.orgSource,
          id: 0
        }
        selectDept(deptSelectedRowRecord.value)
      }
  }

  // 
  if(props.source == "HWORK") {
    budgeterCode.value = loginUser.value?.username
    budgeterName.value = loginUser.value?.nickName
  }
})


// 预算系统
const budgetSystem = ref()
const orgSource = ref()

// 项目名称
const projectCode = ref()
const projectName = ref()
const show = ref(false)

// 法人
const legalPerson = ref()
const legalPersonName = ref()
const legalPersonId = ref()
// 成本中心
const costCenter = ref()
const costCenterName = ref()
// 执行主体
const performCode = ref()
const performName = ref()
// 预算人
const budgeterCode = ref()
const budgeterName = ref()
// 预算主体
const budgetGroupCode = ref()
const budgetGroupName = ref()

// 财务组织编码
const financialCode = ref()
const financialName = ref()

// 预算管理岗
const budgetManager = ref()
const budgetManagerName = ref()

// 研发项目 
const devProjectCode = ref()
const devProjectName = ref()
// 研发项目下拉
const  devProjectOptions = ref([])


// 月度账户id
const accountCode = ref()

// 预算金额
const leftAmt = ref()
// 查询的列表
const searchDataList = ref([]);
// 支付loading
const payLoading = ref<any>(false)

const emit = defineEmits(["setIsPayComplete", "payComplete", "choosedBudget"]);

// 按人查询选中的组织/部门
const userSelectedRowKeys = ref<any>([])
const userSelectedRowRecord = ref<any>({})
const deptSelectedRowKeys = ref<any>([])
const deptSelectedRowRecord = ref<any>({})
const budgets = ref<any>()
// 出账法人下拉值 
const legalPersonOptions = ref<any>([])
// 费用科目
const {
  data: searchFeeItemsData,
  run: searchFeeItemsRun,
  loading: searchFeeItemsLoading
} = useRequest(budgetHaierPayApi.searchFeeItems, {
  manual: false,
  defaultParams: [
    {
      applicationCode: props?.applicationCode,
      budgetSysCode: HaierBudgetSourceConstant.GEMS.code
    }
  ]
});

// 费用科目选项
const feeItemOptions = computed(() => {
  if (searchFeeItemsData.value) {
    return searchFeeItemsData.value.map(it => {
      return { value: it.itemCode, text: it.itemName };
    });
  } else {
    return [];
  }
});

// 费用科目弹窗
const feeItem = ref<any>();
const feeItemName = ref<any>("");
const showPicker = ref<boolean>(false);
const projectOptionsShowPicker  = ref<boolean>(false);



// 选择预算人
const selectBudgeter = (item: ITraveler) => {
  console.log(item)
  budgeterCode.value = item.username;
  budgeterName.value = item.nickName;
};

// 选择预算部门
const selectDept = (item: ITraveler) => {
  console.log(item)
  deptSelectedRowRecord.value = item
  if (deptSelectedRowRecord.value.budgetCode) {
    // 有数据，且选中
    // 清除之前的值
    clear()
    // 赋值A码和B码
    budgetGroupCode.value = deptSelectedRowRecord.value.budgetCode
    budgetGroupName.value = deptSelectedRowRecord.value.financialName
    performCode.value = deptSelectedRowRecord.value.performCode
    performName.value = deptSelectedRowRecord.value.financialName
    financialCode.value = deptSelectedRowRecord.value.financialCode
    financialName.value = deptSelectedRowRecord.value.financialName
    budgetManager.value = deptSelectedRowRecord.value.masterCode
    budgetManagerName.value = deptSelectedRowRecord.value.masterName
    orgSource.value = deptSelectedRowRecord.value.systemCode


    onSearch();
    deptSelectedRowKeys.value = [];
    deptSelectedRowRecord.value = {};
  } else {
    showToast('预算主体为空，查询预算失败！')
    clear()
    return
  }
};


const selectMainInfo = (item: any) => {
  userSelectedRowRecord.value = item
  if (userSelectedRowRecord.value && (userSelectedRowRecord.value.id || userSelectedRowRecord.value.id === 0)) {
    if (userSelectedRowRecord.value.budgetGroupCode) {
      // 有数据，且选中
      // 清除之前的值
      clear()
      // 赋值A码和B码
      budgetGroupCode.value = userSelectedRowRecord.value.budgetGroupCode
      budgetGroupName.value = userSelectedRowRecord.value.budgetGroupName
      performCode.value = userSelectedRowRecord.value.performGroupCode
      performName.value = userSelectedRowRecord.value.performGroupName
      financialCode.value = userSelectedRowRecord.value.financialCode
      financialName.value = userSelectedRowRecord.value.financialName
      budgetManager.value = userSelectedRowRecord.value.budgetMasterCode
      budgetManagerName.value = userSelectedRowRecord.value.budgetMasterName
      orgSource.value = userSelectedRowRecord.value.systemCode

      onSearch();
      userSelectedRowKeys.value = [];
      userSelectedRowRecord.value = {};
    } else {
      showToast('预算主体为空，查询预算失败！')
      clear()
      return
    }
  }
};

const clear = () => {
  // 清除预算主体
  budgetGroupCode.value = null
  budgetGroupName.value = null
  // 清除法人
  legalPerson.value = null
  legalPersonName.value = null
  legalPersonId.value = null
  // 清除成本中心
  costCenter.value = null
  costCenterName.value = null
  // 清除执行主体
  performCode.value = null
  performName.value = null
  // 清除预算系统
  budgetSystem.value = null
  orgSource.value = null
  // 清除项目名称
  projectCode.value = null
  projectName.value = null


  // 清除费用科目
  feeItem.value = null
  feeItemName.value = null
  // 清除预算金额
  leftAmt.value = null
  // 清除管理岗
  financialCode.value = null
  financialName.value = null
  // 清除财务组织
  budgetManager.value = null
  budgetManagerName.value = null
  devProjectCode.value = null
  devProjectName.value = null

  // budgeterName.value = null
  legalPersonOptions.value = []
  devProjectOptions.value = []


}

// 清除其他项目
const clearOther = () => {
  // 清除选中的组织
  userSelectedRowKeys.value = []
  userSelectedRowRecord.value = {}

  // 清除选中的预算
  SubjectSelectedRowKeys.value = []
  SubjectSelectedRowRecord.value = []

  // 清除预算信息
  budgets.value = []
  // 清除出账法人
  legalPersonOptions.value = []
}

// 查询研发项目
const getQueryProject = () =>{
  if(!legalPerson.value) return
  devProjectOptions.value = []
  organizationCenterApi.getQueryProject({budgetDepCode:budgetGroupCode.value,accountCode:legalPerson.value,targetSystemCode:orgSource.value}).then(res=>{
    res.forEach((item,index)=>{
      item.id = index +1
      item.value = item.projectCode
      item.text = item.projectName
      devProjectOptions.value.push(item)
    })
  })
}


const onSearch = () => {
  budgetHaierPayApi
    .searchHBC2Budget({
      applicationCode: 'haierbusiness-business-trip',
      budgeterCode: budgeterCode.value,
      budgetDepartmentCode: budgetGroupCode.value,
      isQueryDept: budgetBelong.value,
      haierBudgetType: 'DEPT_HBC_2',
      businessType: "SLCL"
    })
    .then((res) => {
      // 费用科目
      res.map((item, index) => {
        item.id = index + 1
        item.name = item.systemCode
        item.subname = `费用科目名称:${item.feeItemName}  费用科目编码:${item.feeItem} 预算金额:${item.leftAmt}`
      })
      budgets.value = res
      // 是否是多个
      if (res.length > 0) {
        const data = res[0]
        leftAmt.value = data.leftAmt
        feeItem.value = data.feeItem
        feeItemName.value = data.feeItemName
        budgetSystem.value = data.systemCode
        accountCode.value = data.accountCode
        getBillAndCostCenters(performCode.value, data.feeItem ?? '')
        // 查询研发项目
        // getQueryProject()

      } else {
        showToast('未查询到预算！')
      }
    })
    .finally(() => {
      // userLoading.value = false;
    })
};


// 获取出账法人和成本中心
const getBillAndCostCenters = async (performCode: string, costItemCode: string) => {
  if (!performCode) {
    showToast('未查询到执行主体！')
    return
  }
  if (!costItemCode) {
    showToast('未查询到费用科目！')
    return
  }
  legalPersonOptions.value = []
  const data = await organizationCenterApi.getBillAndCostCenters(performCode, costItemCode)
  if (data && data.length > 0) {
    data.map((item, index) => {
      legalPersonOptions.value.push({
        id: index + 1,
        legalPerson: item.legalPerson ?? '',
        legalPersonName: item.legalPersonName ?? '',
        costCenter: item.costCenter ?? '',
        costCenterName: item.costCenterName ?? '',
        value: item.legalPerson ?? '',
        text: item.legalPersonName ?? '',
      })
      // 如果法人公司只有一个默认选择,多个不选
      if(data.length === 1) {
        legalPerson.value = data[0].legalPerson
        legalPersonName.value = data[0].legalPersonName
        legalPersonId.value = 1
        costCenter.value = data[0].costCenter
        costCenterName.value = data[0].costCenterName
      }
      // 查询研发项目
      getQueryProject()

    })

  }
}

// 选出账法人
const onConfirm = ({ selectedOptions }) => {
  legalPerson.value = selectedOptions[0]?.legalPerson
  legalPersonName.value = selectedOptions[0]?.legalPersonName
  costCenter.value = selectedOptions[0]?.costCenter
  costCenterName.value = selectedOptions[0]?.costCenterName
  showPicker.value = false;
  // 查询研发项目
  getQueryProject()

};

// 选择研发项目
const projectOptionsOnConfirm = ({ selectedOptions }) => {
      devProjectCode.value = selectedOptions[0]?.projectCode
      devProjectName.value = selectedOptions[0]?.projectName
      projectOptionsShowPicker.value = false;
};


const onSelect = (item) => {
  leftAmt.value = item.leftAmt
  feeItem.value = item.feeItem
  feeItemName.value = item.feeItemName
  budgetSystem.value = item.systemCode
  accountCode.value = item.accountCode
  getBillAndCostCenters(performCode.value, item.feeItem ?? '')
}

const payComplete = () => {
  emit('payComplete', true);
};

const pay = () => {
  if (!feeItem.value) {
    showToast('请选择费用科目!');
    return;
  }
  if (budgetBelong.value === 0 && !budgeterCode.value) {
    showToast('请输入预算人!');
    return;
  }
  // if (!leftAmt.value) {
  //   showToast("获取可用余额失败!")
  //   return;
  // }
  // if (leftAmt.value <= 0) {
  //   showToast("可用余额不足!")
  //   return;
  // }
  payLoading.value = true;
  budgetHaierPayApi
    .occupyBudget(
      {
        haierBudgetType: 'DEPT_HBC_2',
        budgeterCode: budgeterCode.value,
        budgeterName: budgeterName.value,
        budgetDepartmentCode: budgetGroupCode.value,
        budgetDepartmentName: budgetGroupName.value,
        accountCompanyCode: legalPerson.value,
        accountCompanyName: legalPersonName.value,
        projectCode: projectCode.value,
        feeItem: feeItem.value,
        feeItemName: feeItemName.value,
        financialCode: financialCode.value,
        financialName: financialName.value,
        budgetManager: budgetManager.value,
        isQueryDept: budgetBelong.value,
        accountCode: accountCode.value,
        performCode: performCode.value,
        performName: performName.value,
        costCenter: costCenter.value,
        costCenterName: costCenterName.value,
        budgetSystemCode: budgetSystem.value,
        devProjectCode:devProjectCode.value,
        devProjectName:devProjectName.value,
        orgSource: orgSource.value,


        // - 通用参数
        orderCode: props.param?.orderCode,
        applicationCode: props.param?.applicationCode,
        payTypes: props.param?.payTypes,
        username: props.param?.username,
        providerCode: props.param?.providerCode,
        amount: Number(props.param?.amount),
        orderDetailsUrl: props.param?.orderDetailsUrl,
        notifyUrl: props.param?.notifyUrl,
        callbackUrl: props.param?.callbackUrl,
        description: props.param?.description,
        payload: props.param?.payload,
        businessType: props.param?.businessType,
        processId: props.param?.processId,
        startApproveFlag: props.param?.startApproveFlag,
        enterpriseCode: props.param?.enterpriseCode,
        paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
        paymentMethod: 2
      },
      {
        applicationCode: props.param?.applicationCode,
        excludes:
          'paySource,haierBudgetType,budgeterCode,orgSource,budgeterName,budgetDepartmentCode,budgetDepartmentName,accountCompanyCode,accountCompanyName,projectCode,feeItem,feeItemName,financialCode,financialName,budgetManager,isQueryDept,accountCode,performCode,performName,costCenter,costCenterName,budgetSystemCode,paymentMethod,payload,startApproveFlag,devProjectCode,devProjectName',
        nonce: props.param?.hbNonce,
        timestamp: props.param?.hbTimestamp,
        sign: props.param?.sign,
      },
    )
    .then((it) => {
      payComplete();
    })
    .finally(() => {
      payLoading.value = false;
    });
};

const onSubmit = () => {
  const params = {
    haierBudgetType: 'DEPT_HBC_2',
    budgeterCode: budgeterCode.value,
    budgeterName: budgeterName.value,
    budgetDepartmentCode: budgetGroupCode.value,
    budgetDepartmentName: budgetGroupName.value,
    accountCompanyCode: legalPerson.value,
    accountCompanyName: legalPersonName.value,
    projectCode: projectCode.value,
    feeItem: feeItem.value,
    feeItemName: feeItemName.value,
    financialCode: financialCode.value,
    financialName: financialName.value,
    budgetManager: budgetManager.value,
    isQueryDept: budgetBelong.value,
    accountCode: accountCode.value,
    performCode: performCode.value,
    performName: performName.value,
    costCenter: costCenter.value,
    costCenterName: costCenterName.value,
    budgetSystemCode: budgetSystem.value,
    budgetAmount: leftAmt.value,
    paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
    paymentMethod: 2,
    leftAmt: leftAmt.value,
    devProjectCode:devProjectCode.value,
    devProjectName:devProjectName.value,
    orgSource: orgSource.value,
  }
  emit('choosedBudget', params);
}

defineExpose({ pay });

</script>

<template>
  <div class="contentBox">
    <van-form @submit="onSubmit">
     
      <van-tabs v-model:active="budgetBelong" @change="changeBelong">
        <van-tab title="个人">
          <userSelectM :source="props.source" :code="loginUser?.username" label="预算信息" type="hbc2" :value="budgeterName" @chose="selectBudgeter"
            @choseMainInfo="selectMainInfo" />
        </van-tab>
        <van-tab title="部门" v-if="props.source!='HWORK'">
          <deptSelectM label="预算信息" :value="financialName" @chose="selectDept" />
        </van-tab>
      </van-tabs>

      <van-field v-model="budgetSystem" input-align="right" :disabled="true" name="预算系统" label="预算系统"
        @click-right-icon="show = true" :right-icon="budgetSystem && budgets && budgets.length > 1 ? 'exchange' : ''"
        :rules="[{ required: true, message: '请填写预算系统' }]" />
      <van-field v-model="performName" input-align="right" :disabled="true" name="执行主体" label="执行主体"
        :rules="[{ required: true, message: '请填写执行主体' }]" />
      <van-field v-model="budgetGroupName" input-align="right" :disabled="true" name="预算主体" label="预算主体"
        :rules="[{ required: true, message: '请填写预算主体' }]" />
      <van-field v-model="leftAmt" :disabled="true" input-align="right" name="预算金额" label="预算金额"
        :rules="[{ required: true, message: '请填写预算金额' }]" />
      <van-field v-model="feeItemName" :disabled="true" input-align="right" name="feeItem" label="费用科目" />
      <van-field v-model="legalPersonName" is-link readonly input-align="right" label-class="czfr" :label-width="60"
        name="出账法人" error-message-align="right" error-message="请谨慎选择出账法人公司" label="出账法人" placeholder="请选择出账法人" @click="showPicker = true" />
      <van-field v-model="costCenterName" input-align="right" :disabled="true" name="成本中心" label="成本中心"
       />
        <van-field
            v-if="budgetSystem == 'BCC'"
            v-model="devProjectName"
            input-align="right"
            readonly
            clearable
            is-link
            name="研发项目"
            label="研发项目"
            :rules="[{ required: false, message: '请填写研发项目' }]"
             @click="projectOptionsShowPicker = true"
          />


      <div style="margin: 16px;">
        <van-button round block type="primary" native-type="submit">
          确定
        </van-button>
      </div>
    </van-form>
  </div>
  <!-- 出账弹窗  -->
  <van-popup v-model:show="showPicker" position="bottom">
    <van-picker :columns="legalPersonOptions" @confirm="onConfirm" @cancel="showPicker = false"
      :columns-field-names="customFieldName" />
  </van-popup>
  <!-- 研发项目弹窗 -->
  <van-popup v-model:show="projectOptionsShowPicker" position="bottom">
    <van-picker
      :columns="devProjectOptions"
      @confirm="projectOptionsOnConfirm"
      @cancel="projectOptionsShowPicker = false"
    />
  </van-popup>

  <!-- 切换预算系统主题 -->
  <van-action-sheet v-model:show="show" title="切换预算系统主体" :actions="budgets" cancel-text="取消" close-on-click-action
    @select="onSelect" />
</template>

<style scoped lang="less">
:deep(.van-tabs__line) {
  width: 125px;
  height: 4px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.1) 100%), #2681FF;
}

:deep(.van-tabs__content) {
  padding-top: 10px;
}

:deep(.czfr) {
  flex: none;
}
</style>

<style>
:root:root {
  --van-button-primary-background: #0073e5;
  --van-radio-checked-icon-color: #0073e5;
  --van-password-input-background: #f2f2f2;
}
</style>