<script setup lang="ts">
import { computed, onMounted, PropType, reactive, ref, watch, h } from 'vue';
import {
  Table as hTable,
  Divider as hDivider,
  Select as hSelect,
  SelectOption as hSelectOption,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  RadioButton as hRadioButton,
  Modal as hModal,
  Input as hInput,
  InputSearch as hInputSearch,
  Loading as hLoading,
  Space as hSpace,
  Button as hButton,
  Row as hRow,
  Spin as hSpin,
  Col as hCol,
  Tabs as hTabs,
  TabPane as hTabPane,
  Image as hImage,
  Tooltip as hTooltip,
  message,
  TableProps,
  InputGroup as hInputGroup,
} from 'ant-design-vue';
import { SearchOutlined, SwapOutlined } from '@ant-design/icons-vue';
import { IPayData } from '@haierbusiness-front/common-libs/src/pay/model/basicModel';
import { budgetHaierPayApi } from '@haierbusiness-front/apis/src/pay/budgetHaierPay';
import {
  HaierBudgetSourceConstant,
  IUserInfo,
  IUserListRequest,
  PaySourceConstant,
  QueryBudgetInfoRes,
  IAbCodeResponse,
  IBudgetHaierHBC2Response,
  IAbCodeDeptResponse
} from '@haierbusiness-front/common-libs';
import { DataType, usePagination, useRequest } from 'vue-request';
import { userApi, organizationCenterApi } from '@haierbusiness-front/apis';
import { Key, TablePaginationConfig } from 'ant-design-vue/lib/table/interface';
import { isMobile } from '@haierbusiness-front/utils';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import DeptHbc2Budget from './deptHbc2Budget.vue';
import DeptWmBudget from './deptWmBudget.vue';

const props = defineProps<{ budgetType?: string; source?:string; param?: IPayData; haierBudgetPayOccupyRequest?:any; }>();


const emit = defineEmits(['chosed', 'payComplete']);




const visible = ref<boolean>(false);

const show = () => {
  visible.value = true;
};
defineExpose({
  show,
});


const cancel = () => {
  visible.value = false

}


const store = applicationStore();
const { loginUser } = storeToRefs(store);

const typeList = ref([
  {
    budgetType: 'DEPT_HBC_2',
    budgetTypeName: 'HBC2',
  },

  {
    budgetType: 'DEPT_WM',
    budgetTypeName: '微幕',
  },
 
])

const currentBudgetType = ref('DEPT_HBC_2')

watch(
  visible, (val) => { 
    if(val && props?.haierBudgetPayOccupyRequest.haierBudgetType) {
      currentBudgetType.value = props?.haierBudgetPayOccupyRequest.haierBudgetType
    }
  },
  {
    deep: true,
    immediate: true
  }
)

const chosedBudget = (params: any) => { 
  emit('chosed', params)
  visible.value = false
}
</script>
<template>
  <h-modal :maskClosable="false" v-model:open="visible" title="选择预算" width="1400px" :footer="null">

    <h-row :align="'middle'" style="margin-top: 2vh;">
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;" >
            预算类型：
        </h-col>
        <!-- v-if="item.budgetTypeName!='HBC'||props.username=='01506579'" -->
        <h-col span="22" class="line-top"  style="text-align: left;">
          <h-radio-group v-model:value="currentBudgetType" v-for="item in typeList" name="budgetTypesGroup">
              <h-radio :value="item.budgetType" :title="item.budgetTypeName">{{ item.budgetTypeName }}</h-radio>
          </h-radio-group>
        </h-col>
        <h-col span="24">
          <dept-hbc-2-budget :source="$props.source" :haierBudgetPayOccupyRequest="$props.haierBudgetPayOccupyRequest" :budgetType="currentBudgetType" v-if="currentBudgetType === 'DEPT_HBC_2'"  @chosed="chosedBudget" @cancel="cancel" ></dept-hbc-2-budget>
          <dept-wm-budget :source="$props.source" :haierBudgetPayOccupyRequest="$props.haierBudgetPayOccupyRequest" :budgetType="currentBudgetType" v-if="currentBudgetType === 'DEPT_WM'" @chosed="chosedBudget" @cancel="cancel"></dept-wm-budget>
        </h-col>
    </h-row>

  </h-modal>


</template>

<style scoped lang="less">
.row {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.line-top {
  margin-top: 4vh;
}

.budget-input {
  width: 12vw;
}

.budget-input-readonly {
  width: 12vw;
  background-color: rgb(245, 245, 245);
  color: #2e2e2e;
}
.tab-radio-group {
  height: 40px;
    line-height: 40px;
  :deep(.ant-radio-button-wrapper) {
    height: 40px;
    line-height: 40px;
  }
}
.legalPersonRow{
  position: relative;
  .tip{
    position: absolute;
    bottom:-20px;
  }
}
</style>
