<script setup lang="ts">
import { computed, onMounted, PropType, reactive, ref, watch, h } from 'vue';
import {
  Table as hTable,
  Divider as hDivider,
  Select as hSelect,
  SelectOption as hSelectOption,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  RadioButton as hRadioButton,
  Modal as hModal,
  Input as hInput,
  InputSearch as hInputSearch,
  Loading as hLoading,
  Space as hSpace,
  Button as hButton,
  Row as hRow,
  Spin as hSpin,
  Col as hCol,
  Tabs as hTabs,
  TabPane as hTabPane,
  Image as hImage,
  Tooltip as hTooltip,
  message,
  TableProps,
  InputGroup as hInputGroup,
} from 'ant-design-vue';
import { SearchOutlined, SwapOutlined } from '@ant-design/icons-vue';
import { IPayData } from '@haierbusiness-front/common-libs/src/pay/model/basicModel';
import { budgetHaierPayApi } from '@haierbusiness-front/apis/src/pay/budgetHaierPay';
import {
  HaierBudgetSourceConstant,
  IUserInfo,
  IUserListRequest,
  PaySourceConstant,
  QueryBudgetInfoRes,
  IAbCodeResponse,
  IBudgetHaierHBC2Response,
  IAbCodeDeptResponse
} from '@haierbusiness-front/common-libs';
import { DataType, usePagination, useRequest } from 'vue-request';
import { userApi, organizationCenterApi } from '@haierbusiness-front/apis';
import { Key, TablePaginationConfig } from 'ant-design-vue/lib/table/interface';
import { isMobile } from '@haierbusiness-front/utils';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';

const props = defineProps<{ budgetType?: string; source?:string; param?: IPayData; haierBudgetPayOccupyRequest?:any; }>();


const emit = defineEmits(['cancel','chosed', 'payComplete']);


// 个人or部门
const budgetBelong = ref(0)

// 预算系统
const budgetSystem = ref()

// 项目名称
const projectCode = ref()
const projectName = ref()

// 法人
const legalPerson = ref()
const legalPersonName = ref()
const legalPersonId = ref()
// 成本中心
const costCenter = ref()
const costCenterName = ref()
// 执行主体
const performCode = ref()
const performName = ref()
// 预算人
const budgeterCode = ref()
const budgeterName = ref()
// 预算主体
const budgetGroupCode = ref()
const budgetGroupName = ref()

// 财务组织编码
const financialCode = ref()
const financialName = ref()

// 预算管理岗
const budgetManager = ref()
const budgetManagerName = ref()
const orgSource = ref()


// 月度账户id
const accountCode = ref()

// 预算金额
const leftAmt = ref()

const payLoading = ref(false);
const userLoading = ref(false);

const feeItem = ref();
const feeItemName = ref();

// 研发项目 
const devProjectCode = ref()
const devProjectName = ref()
// 研发项目下拉
const  devProjectOptions = ref([])

// 查询研发项目
const getQueryProject = () =>{
  if(!legalPerson.value) return
  // budgetDepCode:'BD1000017747',accountCode:'9420',targetSystemCode:'BCC'
  organizationCenterApi.getQueryProject({budgetDepCode:budgetGroupCode.value,accountCode:legalPerson.value,targetSystemCode:orgSource.value}).then(res=>{
    devProjectOptions.value = res
  })
}


const onSearchOk = () => {
  if (userSelectedRowRecord.value && (userSelectedRowRecord.value.id || userSelectedRowRecord.value.id === 0)) {
    if (userSelectedRowRecord.value.budgetGroupCode) {
      // 有数据，且选中
      // 清除之前的值
      clear()
      // 赋值A码和B码
      budgetGroupCode.value = userSelectedRowRecord.value.budgetGroupCode
      budgetGroupName.value = userSelectedRowRecord.value.budgetGroupName
      performCode.value = userSelectedRowRecord.value.performGroupCode
      performName.value = userSelectedRowRecord.value.performGroupName
      financialCode.value = userSelectedRowRecord.value.financialCode
      financialName.value = userSelectedRowRecord.value.financialName
      budgetManager.value = userSelectedRowRecord.value.budgetMasterCode
      budgetManagerName.value = userSelectedRowRecord.value.budgetMasterName
      orgSource.value = userSelectedRowRecord.value.systemCode
      onSearch();
      userSelectedRowKeys.value = [];
      userSelectedRowRecord.value = {};
    } else {
      message.error('预算主体为空，查询预算失败！')
      return
    }
  }
  visibleUserSearch.value = false;
};

const clear = () => {
  // 清除预算主体
  budgetGroupCode.value = null
  budgetGroupName.value = null
  // 清除法人
  legalPerson.value = null
  legalPersonName.value = null
  legalPersonId.value = null
  // 清除成本中心
  costCenter.value = null
  costCenterName.value = null
  // 清除执行主体
  performCode.value = null
  performName.value = null
  // 清除预算系统
  budgetSystem.value = null
  // 清除费用科目
  feeItem.value = null
  feeItemName.value = null
  // 清除预算金额
  leftAmt.value = null
  // 清除管理岗
  financialCode.value = null
  financialName.value = null
  // 清除财务组织
  budgetManager.value = null
  budgetManagerName.value = null

  devProjectCode.value = null
  devProjectName.value = null
  // budgeterName.value = null
  legalPersonOptions.value = []
  devProjectOptions.value = []


  orgSource.value = null
}

// 清除其他项目
const clearOther = () => {
  // 清除选中的组织
  userSelectedRowKeys.value = []
  userSelectedRowRecord.value = {}

  // 清除选中的预算
  SubjectSelectedRowKeys.value = []
  SubjectSelectedRowRecord.value = []

  // 清除预算信息
  budgets.value = []
  // 清除出账法人
  legalPersonOptions.value = []
}

const confirmLoading = ref(false)
const onSearch = () => {
  confirmLoading.value = true
  budgetHaierPayApi
    .searchHBC2Budget({
      applicationCode: 'haierbusiness-business-trip',
      budgeterCode: userName.value,
      budgetDepartmentCode: budgetGroupCode.value,
      isQueryDept: budgetBelong.value,
      haierBudgetType: props.budgetType,
      businessType: 'SLCL',
    })
    .then((res) => {
      confirmLoading.value = false

      // 费用科目
      res.map((item, index) => {
        item.id = index + 1
      })
      budgets.value = res
      // 是否是多个
      if (res.length > 0) {
        const data = res[0]
        leftAmt.value = data.leftAmt
        feeItem.value = data.feeItem
        feeItemName.value = data.feeItemName
        budgetSystem.value = data.systemCode
        accountCode.value = data.accountCode
        projectCode.value = data.projectCode
        projectName.value = data.projectName

        getBillAndCostCenters(performCode.value, data.feeItem ?? '')
        // 查询研发项目
        // getQueryProject()

      } else {
        message.error('未查询到预算！')
      }
    })
    .finally(() => {
      userLoading.value = false;
      confirmLoading.value = false

    })
    .catch(() => {
      confirmLoading.value = false
    })
};

const payComplete = () => {
  emit('payComplete', true);
};


// 按人查询A码和B码
const {
  data: userAbCodeData,
  run: searchAbCodeByUserRun,
  loading: searchUserLoading
} = useRequest(organizationCenterApi.getAbCode);

const userAbCode = computed(() => {
  if (userAbCodeData.value && userAbCodeData.value.length > 0) {
    let list = [] as IAbCodeResponse[]
    userAbCodeData.value.map((item, index) => {
      const data: IAbCodeResponse = {
        ...item,
        id: index
      }
      list = [...list, data]
    })
    return list
  } else {
    return [] as IAbCodeResponse[]
  }
})
const visibleUserSearch = ref(false)

// 按人查询选中的组织/部门
const userSelectedRowKeys = ref<Key[]>([])
const userSelectedRowRecord = ref<IAbCodeResponse>()

const selectUserSelection: TableProps['rowSelection'] = {
  type: 'radio',
  selectedRowKeys: userSelectedRowKeys as any,
  onChange: (selectedRowKeys: Key[], selectedRows: DataType[]) => {
    userSelectedRowKeys.value = selectedRowKeys;
    userSelectedRowRecord.value = selectedRows[0] as unknown as IAbCodeResponse;
  },
}

const userTableColumns = [
  {
    title: '',
    dataIndex: '',
    width: '7%',
  },
  {
    title: '执行主体',
    dataIndex: 'performGroupCode',
    width: '31%',
  },
  {
    title: '预算主体',
    dataIndex: 'budgetGroupCode',
    width: '31%',
  },
  {
    title: '来源系统',
    dataIndex: 'systemCode',
    width: '31%',
  },
]

// 预算信息
const subjectTableColumns = [
  {
    title: '',
    dataIndex: '',
    width: '5%',
  },
  {
    title: '预算系统',
    dataIndex: 'systemCode'
  },
  {
    title: '费用科目名称',
    dataIndex: 'feeItemName'
  },
  {
    title: '费用科目编码',
    dataIndex: 'feeItem'
  },
  {
    title: '预算金额',
    dataIndex: 'leftAmt'
  }
]

const SubjectSelectedRowKeys = ref<Key[]>([0])
const SubjectSelectedRowRecord = ref<IBudgetHaierHBC2Response[]>()

const selectSubjectSelection: TableProps['rowSelection'] = {
  type: 'radio',
  selectedRowKeys: SubjectSelectedRowKeys as any,
  onChange: (selectedRowKeys: Key[], selectedRows: DataType[]) => {
    SubjectSelectedRowKeys.value = selectedRowKeys;
    SubjectSelectedRowRecord.value = selectedRows as unknown as IBudgetHaierHBC2Response[];
  },
}

// 按部门查询
const {
  data: deptAbCodeData,
  run: searchAbCodeByDeptRun,
  loading: searchDeptLoading
} = useRequest(organizationCenterApi.getAbCodeByDept);

const deptTableColumns = [
  {
    title: '',
    dataIndex: '',
    width: '6%',
  },
  {
    title: '部门名称',
    dataIndex: 'financialName',
    width: '21%',
  },
  {
    title: '执行主体编码',
    dataIndex: 'performCode',
    width: '21%',
  },
  {
    title: '预算主体编码',
    dataIndex: 'budgetCode',
    width: '21%',
  },
  {
    title: '来源系统',
    dataIndex: 'systemCode',
    width: '21%',
  },
]



const deptSelectedRowKeys = ref<Key[]>([])
const deptSelectedRowRecord = ref<IAbCodeDeptResponse>()

const selectDeptSelection: TableProps['rowSelection'] = {
  type: 'radio',
  selectedRowKeys: deptSelectedRowKeys as any,
  onChange: (selectedRowKeys: Key[], selectedRows: DataType[]) => {

    deptSelectedRowKeys.value = selectedRowKeys;
    deptSelectedRowRecord.value = selectedRows[0] as unknown as IAbCodeDeptResponse;
  },
}

const deptAbCode = computed(() => {
  if (deptAbCodeData.value && deptAbCodeData.value.length > 0) {
    let list = [] as IAbCodeDeptResponse[]
    deptAbCodeData.value.map((item, index) => {
      const data: IAbCodeDeptResponse = {
        ...item,
        id: index
      }
      list = [...list, data]
    })
    return list
  } else {
    return [] as IAbCodeDeptResponse[]
  }
})

const visibleDeptSearch = ref(false)

const onDeptSearchOk = () => {

  if (deptSelectedRowRecord.value && (deptSelectedRowRecord.value.id || deptSelectedRowRecord.value.id === 0)) {
    if (deptSelectedRowRecord.value.budgetCode) {
      // 有数据，且选中
      // 清除之前的值
      clear()
      // 赋值A码和B码
      budgetGroupCode.value = deptSelectedRowRecord.value.budgetCode
      budgetGroupName.value = deptSelectedRowRecord.value.financialName
      performCode.value = deptSelectedRowRecord.value.performCode
      performName.value = deptSelectedRowRecord.value.financialName
      financialCode.value = deptSelectedRowRecord.value.financialCode
      financialName.value = deptSelectedRowRecord.value.financialName
      budgetManager.value = deptSelectedRowRecord.value.masterCode
      budgetManagerName.value = deptSelectedRowRecord.value.masterName
      orgSource.value = deptSelectedRowRecord.value.systemCode
      onSearch();
      deptSelectedRowKeys.value = [];
      deptSelectedRowRecord.value = {};
    } else {
      message.error('预算主体为空，查询预算失败！')
      return
    }
  }
  visibleDeptSearch.value = false;
}

// 切换主体 
const switchModalShow = ref<boolean>(false)
// 确定切换
const onSwitchOk = () => {

  if (SubjectSelectedRowRecord.value?.length) {
    feeItem.value = SubjectSelectedRowRecord.value[0].feeItem;
    feeItemName.value = SubjectSelectedRowRecord.value[0].feeItemName;
    leftAmt.value = SubjectSelectedRowRecord.value[0].leftAmt;
    budgetSystem.value = SubjectSelectedRowRecord.value[0].systemCode;
    projectCode.value = SubjectSelectedRowRecord.value[0].projectCode;
    projectName.value = SubjectSelectedRowRecord.value[0].projectName;
    getBillAndCostCenters(performCode.value, SubjectSelectedRowRecord.value[0].feeItem ?? '')

  }
  switchModalShow.value = false;
  SubjectSelectedRowRecord.value = [];
}

// 用户选择
const params = ref<IUserListRequest>({
  enterpriseCode: 'haier',
  pageNum: 1,
  pageSize: 20
})

// 预算信息
const budgets = ref<Array<IBudgetHaierHBC2Response>>([])

// 选人组件
const userName = ref()
const nickName = ref()
// 部门名称
const deptName = ref()
// 出账法人
const legalPersonOptions = ref<Array<{ legalPerson: string, legalPersonName: string, costCenter: string, costCenterName: string, id: number }>>([]);


const userNameChange = (userInfo: IUserInfo | undefined) => {
  clear()
  clearOther()
  if (!userInfo) {
    nickName.value = ''
    userName.value = ''
    budgeterCode.value = ''
    budgeterName.value = ''
    projectCode.value = ''
    projectName.value = ''

    return
  }
  userName.value = userInfo.username
  nickName.value = userInfo.nickName
  budgeterCode.value = userInfo.username
  budgeterName.value = userInfo.nickName

  getAbCode()
}

// 切换部门和个人
const onBudgetBelongChange = async (e: any) => {
  const value = e.target.value
  clear()
  clearOther()
  if (value === 0) {
    // 个人
    deptName.value = ''
  } else {
    // 部门
    nickName.value = ''
    userName.value = ''
    budgeterCode.value = ''
    budgeterName.value = ''
  }
}

// 获取A码和B码
const getAbCode = async () => {
  if (budgetBelong.value === 0) {
    await getAbCodeByUser()
  } else {
    await getAbCodeByDept()
  }
}

const getAbCodeByUser = async () => {
  if (!userName.value) {
    return message.error('请补充预算信息！')
  }
  visibleUserSearch.value = true
  await searchAbCodeByUserRun({ userName: userName.value })
}

const getAbCodeByDept = async () => {
  if (!deptName.value) {
    return message.error('请补充预算信息！')
  }
  visibleDeptSearch.value = true
  await searchAbCodeByDeptRun(deptName.value)
}

// 获取出账法人和成本中心
const getBillAndCostCenters = async (performCode: string, costItemCode: string) => {
  if (!performCode) {
    message.error('未查询到执行主体！')
    return
  }
  if (!costItemCode) {
    message.error('未查询到费用科目！')
    return
  }
  legalPersonOptions.value = []
  const data = await organizationCenterApi.getBillAndCostCenters(performCode, costItemCode)

  if (data && data.length > 0) {
    data.map((item, index) => {
      legalPersonOptions.value.push({
        id: index + 1,
        legalPerson: item.legalPerson ?? '',
        legalPersonName: item.legalPersonName ?? '',
        costCenter: item.costCenter ?? '',
        costCenterName: item.costCenterName ?? ''
      })
      // 如果法人公司只有一个默认选择,多个不选
      if(data.length == 1){
        legalPerson.value = data[0].legalPerson
        legalPersonName.value = data[0].legalPersonName
        legalPersonId.value = 1
        costCenter.value = data[0].costCenter
        costCenterName.value = data[0].costCenterName
      }
    })
    // 查询研发项目
    getQueryProject()

  }
}

const onLegalPersonChange = (value: number) => {
  const data = legalPersonOptions.value.find(o => o.id === value)
  if (data) {
    legalPerson.value = data.legalPerson
    legalPersonName.value = data.legalPersonName
    legalPersonId.value = data.id
    costCenter.value = data.costCenter
    costCenterName.value = data.costCenterName
    // 查询研发项目
    getQueryProject()

  }
}

const ondDvProjectCodeChange = (value: number) => {
  const data = devProjectOptions.value.find(o => o.projectCode === value)
  if(data) {
    devProjectName.value = data.projectName
  }else{
    devProjectName.value = null
  }
}


const onSwitch = () => {
  switchModalShow.value = true
}


// 如果有预算信息了 默认给展示出来
watch(props.haierBudgetPayOccupyRequest, (val) => { 
  if(val) {
    if(val.haierBudgetType == 'DEPT_HBC_2' && (props.haierBudgetPayOccupyRequest.budgeterCode || props.haierBudgetPayOccupyRequest.budgetDepartmentName)){
      budgetBelong.value = props.haierBudgetPayOccupyRequest.isQueryDept

      if(budgetBelong.value == 0) {
        userName.value = props.haierBudgetPayOccupyRequest.budgeterCode
        nickName.value = props.haierBudgetPayOccupyRequest.budgeterName
        budgeterCode.value = props.haierBudgetPayOccupyRequest.budgeterCode
        budgeterName.value = props.haierBudgetPayOccupyRequest.budgeterName
        // 个人预算 
        userSelectedRowRecord.value = {
          budgetGroupCode:  props.haierBudgetPayOccupyRequest.budgetDepartmentCode,
          budgetGroupName:  props.haierBudgetPayOccupyRequest.budgetDepartmentName,
          performGroupCode: props.haierBudgetPayOccupyRequest.performCode,
          performGroupName: props.haierBudgetPayOccupyRequest.performName,
          financialCode:  props.haierBudgetPayOccupyRequest.financialCode,
          financialName:  props.haierBudgetPayOccupyRequest.financialName,
          budgetMasterCode: props.haierBudgetPayOccupyRequest.budgetManager,
          budgetMasterName: props.haierBudgetPayOccupyRequest.budgetManager,
          systemCode: props.haierBudgetPayOccupyRequest.orgSource,
          id: 0
        }

        onSearchOk()
      } else {
        deptName.value = props.haierBudgetPayOccupyRequest.budgetDepartmentName
        // 部门预算
        deptSelectedRowRecord.value = {
          budgetCode: props.haierBudgetPayOccupyRequest.budgetDepartmentCode,
          performCode: props.haierBudgetPayOccupyRequest.performCode,
          financialCode:  props.haierBudgetPayOccupyRequest.financialCode,
          financialName:  props.haierBudgetPayOccupyRequest.budgetDepartmentName,
          masterCode: props.haierBudgetPayOccupyRequest.budgetManager,
          masterName: props.haierBudgetPayOccupyRequest.budgetManager,
          systemCode: props.haierBudgetPayOccupyRequest.orgSource,
          id: 0
        }
        onDeptSearchOk()
      }
    }else {
      if(props.source == "HWORK") {
        userName.value = loginUser.value?.username
        nickName.value = loginUser.value?.nickName
        budgeterCode.value = loginUser.value?.username
        budgeterName.value = loginUser.value?.nickName
      }
    }
  }
},
{
  deep: true,
  immediate: true
}

)

onMounted(() => {

})

const filterOption = (input: string, option: any) => {
  console.log(option)
  return option.legalPersonName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};


const chosedBudget = () => {
  if (!feeItem.value) {
    message.error('请选择费用科目!');
    return;
  }
  if (budgetBelong.value === 0 && !budgeterCode.value) {
    message.error('请输入预算人!');
    return;
  }
  const params = {
        haierBudgetType: props.budgetType,
        budgeterCode: budgeterCode.value,
        budgeterName: budgeterName.value,
        budgetDepartmentCode: budgetGroupCode.value,
        budgetDepartmentName: budgetGroupName.value,
        accountCompanyCode: legalPerson.value,
        accountCompanyName: legalPersonName.value,
        projectCode: projectCode.value,
        // projectCode:'************',
        // projectName:'节能冰箱',
        feeItem: feeItem.value,
        feeItemName: feeItemName.value,
        financialCode: financialCode.value,
        financialName: financialName.value,
        budgetManager: budgetManager.value,
        isQueryDept: budgetBelong.value,
        accountCode: accountCode.value,
        performCode:performCode.value,
        performName: performName.value,
        costCenter:costCenter.value,
        costCenterName:costCenterName.value,
        budgetSystemCode: budgetSystem.value,
        paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,
        paymentMethod: 2,
        leftAmt: leftAmt.value,
        devProjectCode:devProjectCode.value,
        devProjectName:devProjectName.value,
        orgSource: orgSource.value,
  }
  emit('chosed', params)
}

const cancel = () => {
  emit('cancel')

}
const store = applicationStore();
const { loginUser } = storeToRefs(store);


</script>
<template>
  <div>
    <h-modal v-model:open="visibleUserSearch" :title="'用户查询选择'" style="width: 1000px" @ok="onSearchOk">
      <h-row :align="'middle'">
        <h-col :span="24">
          <h-table :columns="userTableColumns" :row-key="(record) => record.id" :row-selection="selectUserSelection"
            size="small" :data-source="userAbCode" :loading="searchUserLoading" :pagination="false">
            <template #bodyCell="{ column, text, record, index: detailIndex }">

              <template v-if="column.dataIndex === 'performGroupCode'">
                {{ `${record.performGroupCode}(${record.performGroupName})` }}
              </template>

              <template v-if="column.dataIndex === 'budgetGroupCode'">
                {{ `${record.budgetGroupCode}(${record.budgetGroupName})` }}
              </template>

            </template>
          </h-table>
          <div style="margin-top:20px;font-size:14px;color:brown;">集团平台部门请选择ORG，智家选择BCC，其他领域根据实际情况选择对应组织</div>
        </h-col>
      </h-row>
    </h-modal>
    <!-- 切换个人预算信息 -->
    <h-modal v-model:open="switchModalShow" :title="'切换主体'" style="width: 1000px" @ok="onSwitchOk">
      <h-row :align="'middle'">
        <h-col :span="24">
          <h-table :columns="subjectTableColumns" :row-key="(record) => record.id" size="small"
            :row-selection="selectSubjectSelection" :data-source="budgets" :pagination="false">
          </h-table>
        </h-col>
      </h-row>
    </h-modal>
    <!-- 切换部门预算信息 -->
    <h-modal v-model:open="visibleDeptSearch" :title="'部门查询选择'" style="width: 1000px;" @ok="onDeptSearchOk">
      <h-row :align="'middle'">
        <h-col :span="24">
          <h-table :scroll="{ x: 900, y: 400 }" :columns="deptTableColumns" :row-key="(record) => record.id"
            :row-selection="selectDeptSelection" size="small" :data-source="deptAbCode" :loading="searchDeptLoading"
            :pagination="false">
          </h-table>
        </h-col>
      </h-row>
    </h-modal>
    <h-spin :spinning="confirmLoading">
      <h-row :align="'middle'">
        <!-- hwork跑动计划只能选择个人的预算 -->
        <template v-if="props.source == 'HWORK'">
          <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 预算信息： </h-col>
          <h-col span="6" class="line-top" style="text-align: left">
            <h-row>
              <h-col span="24">
                <div class="row">
                  <h-input @blur="getAbCode" disabled v-model:value="nickName" :size="'large'" style="width: 100%;" />
                  <a-button type="primary" style="width:120px;" :size="'large'" @click="getAbCode">查询组织</a-button>
                </div>
              </h-col>

            </h-row>
          </h-col>
        </template>

        <template v-else>
          <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 预算信息： </h-col>
          <h-col span="6" class="line-top" style="text-align: left">
            <h-row>
              <h-col span="14">
                <div class="row">
                  <user-select v-if="budgetBelong === 0" class="budget-input" :value="nickName" :params="params"
                    :size="'large'" @change="(userInfo: IUserInfo | undefined) => userNameChange(userInfo)" />
                  <h-input @blur="getAbCode" v-else v-model:value="deptName" :size="'large'" style="width: 100%;" />
                  <a-button type="primary" style="width:120px;" :size="'large'" @click="getAbCode">查询组织</a-button>
                </div>
              </h-col>
              <h-col :offset="1" span="9"
                style="text-align: right;    display: flex;align-items: center;justify-content: center;">
                <a-radio-group class="tab-radio-group" v-model:value="budgetBelong" :size="'small'" button-style="solid"
                  style="width: 100%;" @change="onBudgetBelongChange">
                  <a-radio-button :value="0">个人</a-radio-button>
                  <a-radio-button :value="1">部门</a-radio-button>
                </a-radio-group>
              </h-col>
            </h-row>
          </h-col>
        </template>

        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 预算系统： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <div class="row">
            <h-input :disabled="true" v-model:value="budgetSystem" placeholder="" :size="'large'"
              class="budget-input-readonly" style="width: 100%;" />
            <h-tooltip title="切换预算">
              <h-button type="primary" @click="onSwitch" style="width:46px;" :size="'large'"
                :disabled="!(budgets && budgets.length > 0)">
                <template #icon>
                  <SwapOutlined />
                </template>
              </h-button>
            </h-tooltip>
          </div>
        </h-col>
        <h-col v-if="projectCode" span="2" class="line-top" style="font-size: 12px; text-align: right;"> 项目名称： </h-col>
        <h-col v-if="projectCode" span="6" class="line-top" style="text-align: left">
          <h-input v-model:value="projectName" :disabled="true" placeholder="" :size="'large'"
            :title="projectCode + '/' + projectName" class="budget-input-readonly" style="width: 100%;" />
        </h-col>
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 执行主体： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-input v-model:value="performName" :disabled="true" placeholder="" :size="'large'"
            :title="performCode + '/' + performName" class="budget-input-readonly" style="width: 100%;" />
        </h-col>
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 预算主体： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-input v-model:value="budgetGroupName" :size="'large'" :title="budgetGroupCode + '/' + budgetGroupName"
            class="budget-input-readonly" :disabled="true" style="width:100%;" />
        </h-col>
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 预算金额： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-input v-model:value="leftAmt" :size="'large'" class="budget-input-readonly" :disabled="true"
            style="width:100%;" />
        </h-col>
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 费用科目： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-input v-model:value="feeItemName" :disabled="true" placeholder="" :size="'large'"
            :title="feeItem + '/' + feeItemName" class="budget-input-readonly" style="width: 100%;" />
        </h-col>
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 出账法人： </h-col>
        <h-col span="6" class="line-top legalPersonRow" style="text-align: left">
          <h-select v-model:value="legalPersonId" :filter-option="filterOption" :size="'large'" class="budget-input"
            style="width: 100%;" @change="onLegalPersonChange">
            <h-select-option v-for="(item, index) in legalPersonOptions" :value="item.id" :key="index">{{
              item.legalPersonName
            }}</h-select-option>
          </h-select>
          <div class="tip" style="color: red;">请谨慎选择出账法人公司</div>
        </h-col>
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 成本中心： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-input v-model:value="costCenterName" :disabled="true" placeholder="" :size="'large'"
            :title="costCenter + '/' + costCenterName" class="budget-input-readonly" style="width: 100%;" />
        </h-col>

        <!-- 只有预算系统是bcc才有研发项目 -->
        <h-col v-if="devProjectOptions && devProjectOptions.length && budgetSystem == 'BCC'" span="2" class="line-top"
          style="font-size: 12px;text-align: right;"> 研发项目： </h-col>
        <h-col v-if="devProjectOptions && devProjectOptions.length && budgetSystem == 'BCC'" span="6" class="line-top"
          style="text-align: left">
          <h-select allow-clear v-model:value="devProjectCode" :size="'large'" class="budget-input" style="width: 100%;"
            @change="ondDvProjectCodeChange">
            <h-select-option v-for="(item, index) in devProjectOptions" :value="item.projectCode" :key="item.projectCode">{{
              item.projectName }}</h-select-option>
          </h-select>
        </h-col>

      </h-row>
      <h-row>
        <h-divider></h-divider>
      </h-row>
      <h-row style="line-height: 14vh" :align="'middle'">
        <h-col span="6" offset="10">
          <h-button @click="cancel" style="margin-right: 40px;"
            >取消
          </h-button>
          <h-button type="primary"  @click="chosedBudget" :loading="payLoading"
            >确定
          </h-button>
        </h-col>
      </h-row>
    </h-spin>
  </div>
</template>

<style scoped lang="less">
.row {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.line-top {
  margin-top: 4vh;
}

.budget-input {
  width: 12vw;
}

.budget-input-readonly {
  width: 12vw;
  background-color: rgb(245, 245, 245);
  color: #2e2e2e;
}
.tab-radio-group {
  height: 40px;
    line-height: 40px;
  :deep(.ant-radio-button-wrapper) {
    height: 40px;
    line-height: 40px;
  }
}
.legalPersonRow{
  position: relative;
  .tip{
    position: absolute;
    bottom:-20px;
  }
}
</style>
