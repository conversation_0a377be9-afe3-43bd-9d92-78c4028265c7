<script setup lang="ts">
import { computed, onMounted, PropType, reactive, ref, watch, h } from 'vue';
import {
  Table as hTable,
  Divider as hDivider,
  Select as hSelect,
  SelectOption as hSelectOption,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  RadioButton as hRadioButton,
  Modal as hModal,
  Input as hInput,
  InputSearch as hInputSearch,
  Loading as hLoading,
  Space as hSpace,
  Button as hButton,
  Row as hRow,
  Spin as hSpin,
  Col as hCol,
  Tabs as hTabs,
  TabPane as hTabPane,
  Image as hImage,
  Tooltip as hTooltip,
  message,
  TableProps,
  InputGroup as hInputGroup,
} from 'ant-design-vue';
import { SearchOutlined, SwapOutlined } from '@ant-design/icons-vue';
import { IPayData } from '@haierbusiness-front/common-libs/src/pay/model/basicModel';
import { budgetHaierPayApi } from '@haierbusiness-front/apis/src/pay/budgetHaierPay';
import {
  HaierBudgetSourceConstant,
  IUserInfo,
  IUserListRequest,
  PaySourceConstant,
  QueryBudgetInfoRes,
  IAbCodeResponse,
  IBudgetHaierHBC2Response,
  IAbCodeDeptResponse
} from '@haierbusiness-front/common-libs';
import { DataType, usePagination, useRequest } from 'vue-request';
import { userApi, organizationCenterApi } from '@haierbusiness-front/apis';
import { Key, TablePaginationConfig } from 'ant-design-vue/lib/table/interface';
import { isMobile } from '@haierbusiness-front/utils';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'

const props = defineProps<{ budgetType?: string; param?: IPayData }>();


const emit = defineEmits(['chosed', 'payComplete']);



const visible = ref<boolean>(false);
const confirmLoading = ref<boolean>(false);

const budgeDetail = ref<any>()
const show = (res:any) => {
  budgeDetail.value = res
  visible.value = true;
};
defineExpose({
  show,
});

</script>
<template>
  <h-modal :maskClosable="false" :footer="null" v-model:open="visible" title="预算详情" width="1400px" >

    <h-spin :spinning="confirmLoading" >
      <h-row :align="'middle'" v-if="budgeDetail?.haierBudgetType == 'DEPT_HBC_2'">
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 预算信息： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-row>
            <h-col span="14">
              <div class="row">
                <h-input  disabled  :value="budgeDetail.budgeterName" :size="'large'" style="width: 100%;" />
              </div>
            </h-col>
          </h-row>
        </h-col>
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 预算系统： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <div class="row">
            <h-input :disabled="true"  :value="budgeDetail.budgetSystemCode" placeholder="" :size="'large'"
              class="budget-input-readonly" style="width: 100%;" />
            
          </div>
        </h-col>
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 执行主体： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-input :value="budgeDetail.performName" :disabled="true" placeholder="" :size="'large'"
            :title="budgeDetail.performCode + '/' + budgeDetail.performName" class="budget-input-readonly" style="width: 100%;" />
        </h-col>
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 预算主体： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-input :value="budgeDetail.budgetDepartmentName" :size="'large'" :title="budgeDetail.budgetDepartmentCode + '/' + budgeDetail.budgetDepartmentName"
            class="budget-input-readonly" :disabled="true" style="width:100%;" />
        </h-col>
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 费用科目： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-input :value="budgeDetail.feeItemName" :disabled="true" placeholder="" :size="'large'"
            :title="budgeDetail.feeItem + '/' + budgeDetail.feeItemName" class="budget-input-readonly" style="width: 100%;" />
        </h-col>
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 出账法人： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">

          <h-input :value="budgeDetail.accountCompanyName" :disabled="true" placeholder="" :size="'large'"
            class="budget-input-readonly" style="width: 100%;" />
        </h-col>
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 成本中心： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-input :value="budgeDetail.costCenterName" :disabled="true" placeholder="" :size="'large'"
            :title="budgeDetail.costCenter + '/' + budgeDetail.costCenterName" class="budget-input-readonly" style="width: 100%;" />
        </h-col>
        <h-col span="2" class="line-top" style="font-size: 12px;text-align: right;"> 研发项目： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-input :value="budgeDetail.devProjectName" :disabled="true" placeholder="" :size="'large'"
             class="budget-input-readonly" style="width: 100%;" />
        </h-col>
      </h-row>

      <h-row :align="'middle'"  v-if="budgeDetail?.haierBudgetType == 'DEPT_WM'">
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 预算信息： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-row>
            <h-col span="14">
              <div class="row">
                <h-input  disabled  :value="budgeDetail.budgeterName" :size="'large'" style="width: 100%;" />
              </div>
            </h-col>
          </h-row>
        </h-col>
        
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 部门名称： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-input :value="budgeDetail.budgetDepartmentName" :size="'large'" :title="budgeDetail.budgetDepartmentName"
            class="budget-input-readonly" :disabled="true" style="width:100%;" />
        </h-col>
        
      </h-row>
      <h-row>
        <h-divider></h-divider>
      </h-row>
    </h-spin>
  </h-modal>


</template>

<style scoped lang="less">
.row {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.line-top {
  margin-top: 4vh;
}

.budget-input {
  width: 12vw;
}

.budget-input-readonly {
  width: 12vw;
  background-color: rgb(245, 245, 245);
  color: #2e2e2e;
}
.tab-radio-group {
  height: 40px;
    line-height: 40px;
  :deep(.ant-radio-button-wrapper) {
    height: 40px;
    line-height: 40px;
  }
}
</style>
