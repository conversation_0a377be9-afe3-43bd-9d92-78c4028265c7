<script setup lang="ts">
import { computed, onMounted, PropType, reactive, ref, watch, h } from 'vue';
import {
  Table as hTable,
  Divider as hDivider,
  Select as hSelect,
  SelectOption as hSelectOption,
  RadioGroup as hRadioGroup,
  Radio as hRadio,
  RadioButton as hRadioButton,
  Modal as hModal,
  Input as hInput,
  InputSearch as hInputSearch,
  Loading as hLoading,
  Space as hSpace,
  Button as hButton,
  Row as hRow,
  Spin as hSpin,
  DatePicker as hDatePicker,
  Col as hCol,
  Tabs as hTabs,
  TabPane as hTabPane,
  Image as hImage,
  Tooltip as hTooltip,
  message,
  TableProps,
  InputGroup as hInputGroup,
} from 'ant-design-vue';
import { SearchOutlined, SwapOutlined } from '@ant-design/icons-vue';
import { IPayData } from '@haierbusiness-front/common-libs/src/pay/model/basicModel';
import { budgetHaierPayApi } from '@haierbusiness-front/apis/src/pay/budgetHaierPay';
import {
  HaierBudgetSourceConstant,
  IUserInfo,
  IUserListRequest,
  PaySourceConstant,
  QueryBudgetInfoRes,
  IAbCodeResponse,
  IBudgetHaierHBC2Response,
  IAbCodeDeptResponse
} from '@haierbusiness-front/common-libs';
import { DataType, usePagination, useRequest } from 'vue-request';
import { userApi, organizationCenterApi } from '@haierbusiness-front/apis';
import { Key, TablePaginationConfig } from 'ant-design-vue/lib/table/interface';
import { isMobile } from '@haierbusiness-front/utils';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue'
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { storeToRefs } from 'pinia';
import dayjs from 'dayjs';

const props = defineProps<{ budgetType?: string; source?: string; param?: IPayData; haierBudgetPayOccupyRequest?: any; }>();

const emit = defineEmits(['cancel', 'chosed', 'payComplete']);

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const confirmLoading = ref(false)
const budgetMonth = ref()
budgetMonth.value = dayjs().format('YYYY-MM');

const nickName = ref<any>()
const userName = ref<any>()

const budgetGroupName = ref<any>()
const budgetGroupCode = ref<any>()

const leftAmt = ref<any>()

const payLoading = ref(false);


const userNameChange = (userInfo?: IUserInfo | undefined) => {

  if (!userInfo) {
    nickName.value = undefined
    userName.value = undefined
    return
  }
  userName.value = userInfo.username
  nickName.value = userInfo.nickName

  onSearch()
}

const onSearch = () => {
  confirmLoading.value = true
  budgetHaierPayApi
    .searchHBC2Budget({
      applicationCode: 'haierbusiness-business-trip',
      budgeterCode: userName.value,
      haierBudgetType: props.budgetType,
      businessType: 'SLCL',
      budgetMonth: dayjs(budgetMonth.value).month() + 1
    })
    .then((res) => {
      confirmLoading.value = false
      budgetGroupName.value = res[0].budgetDepartmentName
      budgetGroupCode.value = res[0].budgetDepartmentCode
      leftAmt.value = res[0].leftAmt
    })
    .finally(() => {
      confirmLoading.value = false

    })
    .catch(() => {
      confirmLoading.value = false
      budgetGroupName.value = ""
      budgetGroupCode.value = ""
      leftAmt.value = ""
    })
};

const chosedBudget = () => {
  if (!budgetGroupName.value) {
    message.error('请选择预算信息')
    return

  }
  if (!leftAmt.value) {
    message.error('请选择预算信息')
    return

  }
  // 
  const params = {
    budgetDepartmentCode: budgetGroupName.value,
    budgetDepartmentName: budgetGroupCode.value,
    budgetMonth: dayjs(budgetMonth.value).month() + 1,
    leftAmt: leftAmt.value,
    businessType: "SLCL",
    haierBudgetType: props.budgetType,
    budgeterCode: userName.value,
    budgeterName: nickName.value,
    paySource: isMobile() ? PaySourceConstant.MOBILE.type : PaySourceConstant.PC.type,

  }
  emit('chosed', params)
}

const cancel = () => {
  emit('cancel')
}

// 如果有预算信息了 默认给展示出来
watch(props.haierBudgetPayOccupyRequest, (val) => {
  if (val) {
    if (val.haierBudgetType == 'DEPT_WM') {

      userName.value = val.budgeterCode
      nickName.value = val.budgeterName
      onSearch()

    } else {
      if (props.source == "HWORK") {
        userName.value = loginUser.value?.username
        nickName.value = loginUser.value?.nickName
      }
    }
  }
},
  {
    deep: true,
    immediate: true
  }
)


</script>
<template>
  <div>

    <h-spin :spinning="confirmLoading">
      <h-row :align="'middle'">
        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 预算月份： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <div class="row">
            <!-- <h-input :disabled="true" v-model:value="budgetMonth" placeholder="" :size="'large'"
              class="budget-input-readonly" style="width: 100%;" /> -->
            <h-date-picker :disabled="true" style="width: 100%;" valueFormat="YYYY-MM" placeholder="请选择预算月份"
              v-model:value="budgetMonth" picker="month" />
          </div>
        </h-col>
        <!-- hwork跑动计划只能选择个人的预算 -->
        <template v-if="props.source == 'HWORK'">
          <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 预算信息： </h-col>
          <h-col span="6" class="line-top" style="text-align: left">
            <h-row>
              <h-col span="24">
                <div class="row">
                  <h-input @blur="onSearch" disabled v-model:value="nickName" :size="'large'" style="width: 100%;" />
                  <a-button type="primary" style="width:120px;" :size="'large'" @click="onSearch">查询组织</a-button>
                </div>
              </h-col>

            </h-row>
          </h-col>
        </template>

        <template v-else>
          <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 预算信息： </h-col>
          <h-col span="6" class="line-top" style="text-align: left">
            <h-row>
              <h-col span="24">
                <div class="row">
                  <user-select placeholder="预算信息" class="budget-input" :value="nickName" :params="params" :size="'large'"
                    @change="(userInfo: IUserInfo | undefined) => userNameChange(userInfo)" />
                  <a-button type="primary" style="width:120px;" :size="'large'" @click="onSearch">查询组织</a-button>
                </div>
              </h-col>

            </h-row>
          </h-col>
        </template>



        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 部门名称： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-input placeholder="部门名称" v-model:value="budgetGroupName" :size="'large'"
            :title="budgetGroupCode + '/' + budgetGroupName" class="budget-input-readonly" :disabled="true"
            style="width:100%;" />
        </h-col>

        <h-col span="2" class="line-top" style="font-size: 12px; text-align: right;"> 预算金额： </h-col>
        <h-col span="6" class="line-top" style="text-align: left">
          <h-input placeholder="预算金额" v-model:value="leftAmt" :size="'large'" class="budget-input-readonly"
            :disabled="true" style="width:100%;" />
        </h-col>

      </h-row>
      <h-row>
        <h-divider></h-divider>
      </h-row>
      <h-row style="line-height: 14vh" :align="'middle'">
        <h-col span="6" offset="10">
          <h-button @click="cancel" style="margin-right: 40px;">取消
          </h-button>
          <h-button type="primary" @click="chosedBudget" :loading="payLoading">确定
          </h-button>
        </h-col>
      </h-row>
    </h-spin>
  </div>
</template>

<style scoped lang="less">
.row {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.line-top {
  margin-top: 4vh;
}

.budget-input {
  width: 12vw;
}

.budget-input-readonly {
  width: 12vw;
  background-color: rgb(245, 245, 245);
  color: #2e2e2e;
}

.tab-radio-group {
  height: 40px;
  line-height: 40px;

  :deep(.ant-radio-button-wrapper) {
    height: 40px;
    line-height: 40px;
  }
}

.legalPersonRow {
  position: relative;

  .tip {
    position: absolute;
    bottom: -20px;
  }
}
</style>
