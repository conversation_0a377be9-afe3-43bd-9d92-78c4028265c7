<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { meetingHandoversApi } from '@haierbusiness-front/apis';
import {
  IMeetingHandoversFilter,
  IMeetingHandovers
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted, nextTick, reactive } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import router from '../../router'
import type { MenuItemType, MenuInfo } from 'ant-design-vue/lib/menu/src/interface';
import Actions from '@haierbusiness-front/components/actions/Actions.vue';
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  listApiRun({
    pageNum: 1,
    pageSize: 10
  })
})

const columns: ColumnType[] = [
  {
    title: '经办人',
    dataIndex: 'connectBeforeName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '经办人电话',
    dataIndex: 'connectBeforePhone',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '经办人邮箱',
    dataIndex: 'connectBeforeEmail',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '经办人工号',
    dataIndex: 'connectBeforeCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '承接人',
    dataIndex: 'handoverAfterName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '承接人电话',
    dataIndex: 'handoverAfterPhone',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '承接人邮箱',
    dataIndex: 'handoverAfterEmail',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '承接人工号',
    dataIndex: 'handoverAfterCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '承接人直线',
    dataIndex: 'handoverLineName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '承接原因',
    dataIndex: 'handoverReason',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'handoverState',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      const content = text === 10 ? '交接中' : text === 20 ? '已完成' : text === 30 ? '已驳回 ' : '已撤回';
      return {
        children: content,
        props: {
          style: {
            textAlign: 'center'
          }
        }
      };
    },
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
let searchParam = reactive({
  operator: '',
  handover: '',
  handoverState: undefined,
  startDate: '',
  endDate: '',
})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(meetingHandoversApi.list);

const reset = () => {
  // 保持响应式引用，仅修改属性
  Object.assign(searchParam, {
    operator: '',
    handover: '',
    handoverState: undefined,
    startDate: '',
    endDate: '',
  })
  createTime.value = undefined
  // 使用nextTick确保DOM更新后再调用接口
  nextTick(() => {
    // 重置后立即查询
    const params = {
      pageNum: 1,
      pageSize: 10
    };
    console.log('重置后调用接口参数:', params);
    listApiRun(params);
  });
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } =
  useEditDialog<IMeetingHandovers, IMeetingHandovers>(meetingHandoversApi, "会议交接", () => listApiRun({
    ...searchParam,
    pageNum: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
  }))


const handledetails = (item: IMeetingHandovers) => {
  currentRouter.value.push({
    path: '/bidman/meetingHandovers/details',
    query: {
      id: item.id
    }
  })
}

// 删除
const { handleDelete } = useDelete(meetingHandoversApi, () => listApiRun({
  ...searchParam,
  pageNum: data.value?.pageNum,
  pageSize: data.value?.pageSize,
}))

const createTime = ref<[Dayjs, Dayjs]>()
watch(() => createTime.value, (n: any, o: any) => {
  if (n) {
    searchParam.startDate = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    searchParam.endDate = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    searchParam.startDate = ''
    searchParam.endDate = ''
  }
});

const handleRevoke = async (record: any) => {
  console.log(record,"record");
  
  const processUrl = `https://travel-test.haier.net/hbweb/process/?code=${record.approvalCode}#/details`;
  window.open(processUrl, '_blank');

}

// 处理菜单点击事件
const handleMenuClick = (record: any, e: MenuInfo) => {
  const key = e.key as string;
  switch (key) {
    case 'revoke':
      if (record.handoverState == 10) {
        handleRevoke(record);
      }
      break;
  }
};

// 计算菜单选项
const getMenuOptions = (record: any) => {
  const options: MenuItemType[] = [];

  if (record.handoverState === 10) {
    options.push({
      key: 'revoke',
      label: '审批查看',
    });
  }


  return options;
};

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="operator">经办人：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="operator" v-model:value="searchParam.operator" placeholder="请输入经办人" allow-clear
              :maxlength="500" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="handover">承接人：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="handover" v-model:value="searchParam.handover" placeholder="请输入承接人" allow-clear
              :maxlength="500" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="handoverState">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select id="type" v-model:value="searchParam.handoverState" placeholder="请选择状态" class="full-width"
              allow-clear style="width: 100%;">
              <h-select-option :value="10">交接中</h-select-option>
              <h-select-option :value="20">已完成</h-select-option>
              <h-select-option :value="30">已驳回</h-select-option>
              <h-select-option :value="40">已撤回</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createTime">创建时间：</label>
          </h-col>
          <h-col :span="4" class="margin-top">
            <h-range-picker v-model:value="createTime" value-format="YYYY-MM-DD" class="full-width" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource" :scroll="{ x: 1400 }"
          :pagination="pagination" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="handledetails(record)">查看</h-button>
              <Actions :menu-options="getMenuOptions(record)" :on-menu-click="(e) => handleMenuClick(record, e)"
                v-if="getMenuOptions(record).length > 0"></Actions>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

    <div v-if="visible">
      <edit-dialog :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleOk">
      </edit-dialog>
    </div>

  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
</style>
