<script setup lang="ts">
import logo from '@/assets/image/logo.png';
import haierit from '@/assets/image/haierit.ico';
import sy1 from '@/assets/image/sy1.jpeg';
import sy2 from '@/assets/image/sy2.jpeg';
import { Spin as hSpin, Space as hSpace, Input as hInput, InputPassword as hInputPassword, Carousel as hCarousel, Button as hButton, Col as hCol, Result as hResult, Row as hRow, TabPane as hTabPane, Tabs as hTabs } from 'ant-design-vue';
import { onMounted, PropType, ref } from 'vue';
import { loginApi } from '@haierbusiness-front/apis';
import md5 from 'js-md5';

import {ILoginResult,IIamLocalLogin} from '@haierbusiness-front/common-libs';



const props = defineProps({
    param: Object as PropType<IIamLocalLogin>
});

const emit = defineEmits<{
    (e: 'loginSuccess', result: ILoginResult): void
}>()

const loginSuccess = (result: ILoginResult) => {
    emit('loginSuccess', result)
}

const loading = ref(false)
const username = ref("")
const password = ref("")
const onChange = ref()
const imgs = ref([sy1, sy2])
const activeKey = ref("1")

loading.value = false;


const uernamelogin = () => {
    loading.value = true;
    loginApi.usernameLogin({
        username: username.value,
        password: password.value
    }).then(it => {
        loginSuccess({data:it})
    }).finally(() => {
        loading.value = false;
    })
}
</script>
<template>
    <div class="background-width-a"></div>
    <div class="login-body">
        <div class="haierbusiness-title">
            <div class="title-text">
                <img :src="logo" style="height: 3vh;margin-bottom: 0.8vh;">
            </div>
        </div>
        <div class="login-frame">
            <h-row>
                <h-col :span="12">
                    <div class="login-form">
                        <h-row class="login-form-body">
                            <h-tabs v-model:activeKey="activeKey">
                                <h-tab-pane key="1" tab="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;密码登录&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;">
                                    <h-row style="margin: 5vh;">
                                        <h-space direction="vertical" :size=25 style="width: 20vw;height: 100%;">
                                            <h-input v-model:value="username" placeholder="用户名" size="large"
                                                style="height: 100%; " />
                                            <h-input-password v-model:value="password" placeholder="密码" size="large"
                                                style="height: 100%;" />
                                            <h-button type="primary" style="width: 100%;" @click="uernamelogin"
                                                :loading="loading"
                                                size="large">&nbsp;&nbsp;登&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;录&nbsp;&nbsp;</h-button>
                                        </h-space>
                                    </h-row>
                                </h-tab-pane>
                                <h-tab-pane key="2"
                                    tab="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;手机登录&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;">Content of
                                    Tab
                                    开发中...
                                </h-tab-pane>
                            </h-tabs>
                        </h-row>
                        <h-row class="other-login-typs">
                            <h-col :span="22" :offset="1">
                                <h-row>
                                    <h-col :span="8">
                                        <div class="other-text-line"></div>
                                    </h-col>
                                    <h-col :span="8">
                                        <div class="other-text-container">
                                            <span class="other-text">其他方式登录</span>
                                        </div>
                                    </h-col>
                                    <h-col :span="8">
                                        <div class="other-text-line"></div>
                                    </h-col>
                                </h-row>
                            </h-col>
                            <h-col :span="24">
                                <h-row>
                                    <h-col :span="17" :offset="7">
                                        <div style="margin-top: 5vh;">
                                            <img :src="haierit" style="height: 3.2vh;">
                                            <span style="font-size: 1.8vh;line-height: 1.8vh;"> &nbsp;使用海尔统一登录</span>
                                        </div>
                                    </h-col>
                                </h-row>
                            </h-col>
                        </h-row>
                    </div>
                </h-col>
                <h-col :span="12">
                    <div class="carousel-container">
                        <h-carousel :after-change="onChange" autoplay>
                            <img class="carousel-img" v-for="item of imgs" :src="item">
                        </h-carousel>
                    </div>
                </h-col>
            </h-row>
        </div>
    </div>
</template>
<style scoped lang="less">
.background-width-a {
    float: left;
    position: absolute;
    width: 58vw;
    height: 58vh;
    background-image: url(../assets/image/people.png);
    background-repeat: no-repeat;
    background-size: contain;
    z-index: -1;
    top: 42vh;
}

.login-body {
    min-width: 1024px;
    min-height: 100vh;
    height: 100vh;
    width: 100vw;
    background-color: rgba(250, 250, 250, 0.301);

    .haierbusiness-title {
        height: 16vh;
        margin: auto;
        padding-top: 8vh;

        .title-text {
            text-align: center;
            font-size: 2.6vh;
            font-weight: 600;
            line-height: 1vh;
            color: rgb(43, 42, 42);
        }
    }

    .login-frame {
        margin-top: 16vh;
        border-radius: 1vh;
        height: 500px;
        width: 50vw;
        margin: auto;
        background-color: rgba(242, 243, 243);
        box-shadow: 0px 20px 80px 0px rgb(0 0 0 / 30%);


        .login-form {
            padding: 1vh;

            .login-form-body {
                height: 340px;
            }

            .other-login-typs {
                .other-text-line {
                    margin-top: 1vh;
                    width: 100%;
                    border-top: 1px solid #8a8686;
                }

                .other-text-container {
                    text-align: center;

                    .other-text {
                        color: #8a8686;
                    }
                }

            }

            .carousel-container {
                height: 100%;
                width: 100%;
            }
        }


        .bwdv-carousel :deep(.slick-slide) {
            text-align: center;
            height: 500px;
            line-height: 500px;
            width: 50vw;
            background: rgb(214, 214, 214);
            overflow: hidden;
            border-top-right-radius: 1vh;
            border-bottom-right-radius: 1vh;
        }

        .bwdv-carousel :deep(.slick-slide h3) {
            color: #fff;
        }

        .carousel-img {
            height: 500px+1px;
            width: 100%;
            object-fit: fill;
        }
    }
}
</style>