<template>
  <div style="height: 100%; width: 100%; overflow: auto">
    <div :key="item.id" class="indexItemBox">
      <div class="indexItemHeader">
        <div class="headerLeft">
          <a-progress
            @click="toCredibilityRating(item)"
            type="circle"
            :percent="item.credibilityVo?.checkScore"
            status="normal"
            :size="30"
          />
          <span>可信度</span>
        </div>
        <div class="reportName">
          {{ item.reportName }}
          <a-popover title="指标详情">
            <template #content>
              <p>1.指标定义</p>
              <p>2.其他描述</p>
            </template>
            <QuestionCircleOutlined style="color: #7f7f7f" />
          </a-popover>
        </div>
        <div class="headerRight">
          <a-tag v-for="(v, index) in item.labelVo" v-show="index < 2" color="#1677ff">{{ v.labelName }}</a-tag>
        </div>
      </div>
      <div class="indexItemBottom">
        <img style="height: 100%" :src="item.imagesUrl" alt="" />
      </div>
    </div>
    <h-card title="可信度评分" style="padding: 8px">
      <div class="w-500">
        <h-form ref="formRef" :model="form" :label-col="{ span: 7 }" :wrapper-col="{ span: 7 }" :rules="rulesRef">
          <h-form-item label="核对标题" name="checkTitle">
            <h-input v-model:value="form.checkTitle" style="width: 100%" />
          </h-form-item>
          <h-form-item label="核对日期" name="checkDatetime">
            <h-range-picker valueFormat="YYYY-MM-DD" style="width: 100%" v-model:value="form.checkDatetime" />
          </h-form-item>
          <h-form-item label="核对过程描述" name="checkDescribe">
            <h-textarea v-model:value="form.checkDescribe" style="width: 100%" />
          </h-form-item>
          <h-form-item label="核对人" name="checkUserId">
            <user-select
              :value="nickName"
              ref="userRef"
              placeholder="请选择用户"
              :multiple="true"
              :params="{
                pageNum: 1,
                pageSize: 20,
              }"
              @change="(userInfo: IUserInfo) =>  changeUser(userInfo)"
            ></user-select>
          </h-form-item>
          <h-form-item label="可信度得分" name="checkScore">
            <a-input-number id="inputNumber" v-model:value="form.checkScore" :precision="1" :min="1" :max="100" />
          </h-form-item>
          <h-form-item label="核对附件" name="approvalFlowType">
            <a-upload-dragger
              v-model:file-list="fileList"
              accept="*"
              :max-count="10"
              class="avatar-uploader"
              :action="'/hbweb/data/hb/common/api/file/upload'"
              :headers="{
                'Hb-Token': token,
              }"
              :multiple="true"
              @change="handleChange"
              :before-upload="beforeUpload"
            >
              <p class="ant-upload-drag-icon">
                <inbox-outlined></inbox-outlined>
              </p>
              <p class="ant-upload-text">点击或将文件拖拽到这里上传</p>
              <p class="ant-upload-hint">支持扩展名：.rar .zip .doc .docx .pdf .jpg...</p>
            </a-upload-dragger>
            <h-row justify="center">
              <div class="flexCon">
                <h-space>
                  <h-button @click="handleReset">重置</h-button>
                  <h-button @click="handleOk" type="primary" html-type="submit" :loading="confirmLoading"
                    >提交</h-button
                  >
                </h-space>
              </div>
            </h-row>
          </h-form-item>
        </h-form>
      </div>
    </h-card>
  </div>
</template>

<script setup lang="ts">
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Switch as hSwitch,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Image as hImage,
  Card as hCard,
  RangePicker as hRangePicker,
  InputNumber as hInputNumber,
  UploadDragger as hUploadDragger,
  Row as hRow,
  Space as hSpace,
  Button as hButton,
  message,
} from 'ant-design-vue';

import {
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
} from '@ant-design/icons-vue';
import {
  ISmartBrainFilter,
  ISmartBrain,
  Datum,
  IReportManagerInfoListList,
  deptLabelVo,
  IUserInfo,
  FetchBrainDataParams,
} from '@haierbusiness-front/common-libs';
import { getCurrentRouter, routerParam } from '@haierbusiness-front/utils';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
import { HeaderConstant } from '@haierbusiness-front/common-libs';
import type { Rule } from 'ant-design-vue/es/form';
import { smartBrainApi } from '@haierbusiness-front/apis';
import { computed, ref, watch, onMounted } from 'vue';
const router = getCurrentRouter();
import { useRoute } from 'vue-router';
const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);
const route = useRoute();
const action = import.meta.env.VITE_UPLOAD_UR;
const form = ref<any>({ checkUserId: [], checkUserName: [] });
const nickName = ref<Array<string>>([]);
const item = ref<any>({});
const formRef = ref();
const userRef = ref<any>(null);
const changeUser = (userInfo: Array<any>) => {
  console.log(userInfo);
  if (!userInfo.length) {
    form.value.checkUserId = [];
    form.value.checkUserName = [];
  }
  const array: Array<string> = [];
  userInfo.forEach((item: any, index: number) => {
    if (item.nickName && item.username) {
      array.push(item.nickName + '/' + item.username);
      form.value.checkUserId.push(item.username);
      form.value.checkUserName.push(item.nickName);
    } else {
      array.push(
        nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[0] +
          '/' +
          nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[1],
      );
      form.value.checkUserId.push(nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[1]);
      form.value.checkUserName.push(nickName.value.find((o) => o.split('/')[1] === item)?.split('/')[0]);
    }
  });
  nickName.value = array;
};
const rulesRef: Record<string, Rule[]> = {
  checkTitle: [{ required: true, message: '请输入核对标题' }],
  checkDatetime: [{ required: true, message: '选择核对日期' }],
  checkUserId: [{ required: true, message: '选择核对人' }],
};

const confirmLoading = ref<boolean>(false);
const fileList = ref<any[]>([]);
const beforeUpload = (file: UploadFile) => {
  const isLt5M = file.size && file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    message.error('文件大小不能超过5M!');
  }
  return isLt5M;
};
const handleChange = (info: UploadChangeParam) => {
  console.log(info);
  if (info.file.status === 'uploading') {
  }

  if (info.file.status === 'done') {
    // information.value.imgUrl = info.file.response.content.url;
    fileList.value = info.fileList;
  }
  if (info.file.status === 'error') {
    // loading.value = false;
    message.error('上传出错！');
  }
};
// 根据指标id获取详情
// 获取指标详情
const getBrainReportIndicatorById = () => {
  smartBrainApi.getBrainReportIndicatorById({ id: route.query.id }).then((res: any) => {
    item.value = res;
  });
  smartBrainApi.getCredibilityByEchartsId({ id: route.query.id }).then((res: any) => {
    res.checkDatetime = res.checkDatetime.split(',');
    res.checkUserId = res.checkUserId.split(',');
    res.checkUserName = res.checkUserName.split(',');
    res.checkUserId.forEach((item: any, index: number) => {
      nickName.value.push(res.checkUserName[index] + '/' + item);
    });
    res.checkFileUrl.forEach((item: any, index: number) => {
      fileList.value.push({ name: res.checkFileName[index], url: item });
    });
    userRef.value.setFirstData(res.checkUserId);
    form.value = res;
  });
};

// 提交表单
const handleOk = () => {
  formRef.value
    .validate()
    .then((res: any) => {
      confirmLoading.value = true;
      let obj = JSON.parse(JSON.stringify(form.value));
      obj.checkUserId = obj.checkUserId.toString();
      obj.checkUserName = obj.checkUserName.toString();
      obj.checkDatetime = obj.checkDatetime.toString();
      obj.reportId = route.query.id;
      obj.checkFileUrl = [];
      obj.checkFileName = [];
      fileList.value.forEach((item: any) => {
        if (
          window.location.origin.indexOf('http://127.0.0.1') !== -1 ||
          window.location.origin.indexOf('http://localhost') !== -1
        ) {
          if (item.response) {
            obj.checkFileUrl.push(`https://travel-test.haier.net/${item.response.data.path}`);
          } else {
            obj.checkFileUrl.push(item.url);
          }
        } else {
          if (item.response) {
            obj.checkFileUrl.push(`${window.location.origin}/${item.response.data.path}`);
          } else {
            obj.checkFileUrl.push(item.url);
          }
        }
        obj.checkFileName.push(item.name);
      });
      smartBrainApi
        .createOrUpdateCredibility(obj)
        .then((res: any) => {
          confirmLoading.value = false;
          message.success(`提交成功`);
          router.push({ path: '/data/smartBrain/IndicatorMng' });
        })
        .catch(() => {
          confirmLoading.value = false;
        });
    })
    .catch((error: any) => {
      console.log('error', error);
    });
};
// 加载完成后监听鼠标事件
onMounted(() => {
  // 获取可拖拽的申请的指标
  if (route.query.id) {
    getBrainReportIndicatorById();
  }
});
</script>

<style lang="less" scoped>
.flexCon {
  margin-top: 24px;
}
.indexItemBox {
  display: flex;
  flex-flow: column;
  width: 100%;
  min-height: 30vh;
  height: 30vh;
  background: inherit;
  background-color: rgba(255, 255, 255, 1);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  margin-bottom: 16px;
  &:nth-child(3n) {
    margin-right: 0;
  }
  .indexItemBottom {
    flex: 1;
    height: calc(30vh - 48px);
    text-align: center;
  }
  .indexItemHeader {
    height: 48px;
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    border-bottom: 1px solid #ccc;
    .reportName {
      width: 30%;
      font-weight: 900;
      font-size: 14px;
      color: #000;
      text-align: center;
    }
    .headerLeft {
      width: 30%;
      position: relative;
      cursor: pointer;
      span {
        position: absolute;
        font-weight: 900;
        font-style: oblique;
        font-size: 8px;
        color: #000000;
        bottom: 0px;
        width: 30px;
        left: 2px;
        font-family: 'HarmonyBold';
      }
    }
    .headerRight {
      width: 30%;
      text-align: right;
    }
  }
}
</style>
>
