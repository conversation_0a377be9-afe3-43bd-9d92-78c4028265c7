<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  FloatButton as hFloatButton,
  Popover as hPopover,
  message
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, EditOutlined, QuestionCircleOutlined, MenuOutlined, ShoppingCartOutlined } from '@ant-design/icons-vue';
import { smartBrainApi } from '@haierbusiness-front/apis';
import { ISmartBrainFilter, ISmartBrain, Datum } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import EditDialog from './edit-dialog.vue';
import router from '../../../router';
// const router = useRouter()
const openHelpModal = ref<boolean>(false)
const currentRouter = ref();
const plainOptions = ref<Datum[]>([]);
const searchParam = ref<ISmartBrainFilter>({
  labelIds:[],
  labelIds1:[],
  labelIds2:[]
});
// 当前选中的指标
const selectIndexArray = ref<any>([]);

// 新增弹窗
const { visible, editData, handleCreate, handleEdit, handleOk } = useEditDialog<
  ISmartBrain,
  ISmartBrain
>(smartBrainApi, '指标', () =>
  listApiRun({
    labelIds:[...searchParam.value.labelIds,...searchParam.value.labelIds1,...searchParam.value.labelIds2],
    pageNo: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 9999,
  }),
);

const { data, run: listApiRun, loading, current, pageSize } = usePagination(smartBrainApi.list);

const reset = () => {
  searchParam.value.labelIds = []
  searchParam.value.labelIds1= []
  searchParam.value.labelIds2 = []
  handleTableChange({ current: 1, pageSize: 9999 });
};


const dataSource = computed(() => data.value?.list || []);

// 删除
const { handleDelete } = useDelete(smartBrainApi, () =>
  listApiRun({
    labelIds:[...searchParam.value.labelIds,...searchParam.value.labelIds1,...searchParam.value.labelIds2],
    pageNo: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }),
);

const beginAndEnd = ref<[Dayjs, Dayjs]>();

//监听
watch(
  () => data.value?.list,
  (n: any, o: any) => {
    console.log(n,o)
    if(n&&n.length){
      selectIndexArray.value = []
      n.forEach((item:any)=>{
        if(item.applyStatus==1){
          selectIndexArray.value.push(item)
        }
      })
    }
  },
);

watch(current, () => {
  console.log(current.value)
  handleTableChange({ current: current.value, pageSize: 9 });
});

// 请求标签list列表
const getBrainKnowLabelList = () => {
  smartBrainApi.getBrainKnowLabelList().then((res: Array<Datum>) => {
    plainOptions.value.push(...res);
  });
};

// 查询
const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    labelIds:[...searchParam.value.labelIds,...searchParam.value.labelIds1,...searchParam.value.labelIds2],
    pageNo: pag.current,
    pageNum:pag.current,
    pageSize: pag.pageSize,
  });
};

// 申请
const goApply = () =>{
  if(!selectIndexArray.value.length){
    message.warning("请选择要申请的指标!");
    return
  }
  visible.value = true
}

// 选中指标列表
const indexChange = (value:any,item:any) =>{
// 如果是选中状态
  if(value.target.checked){
    selectIndexArray.value.push(item)
  }else{
    // 非选中状态取消选中
    selectIndexArray.value.splice(
    selectIndexArray.value.findIndex((v:any)=> {
      return item.id ==  v.id
    }),1)
  }
}

// 关闭指标弹窗
const onDialogClose =()=>{
  // selectIndexArray.value = []
  visible.value = false
}

// 指定当前状态是否选中
 const isChecked = (id:number)=>{
    if(selectIndexArray.value.findIndex((v:any)=> {
      return id == v.id
    })!=-1){
      return true
    }else{
      return false
    }
 }

 const removeReportItem = (item:any) =>{
    selectIndexArray.value.splice(
    selectIndexArray.value.findIndex((v:any)=> {
      return item.id ==  v.id
    }),1)
}

// 初始化
onMounted(async () => {
  currentRouter.value = await router;
  getBrainKnowLabelList();
  handleTableChange({ current: 1, pageSize: 9999 });
  console.log(data)
});
</script>

<template>
  <div style="height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <a-card>
      <h-row :align="'middle'">
        <h-col :span="24" style="margin-bottom: 10px">
          <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
            <h-col :span="2" style="text-align: right; padding-right: 10px">
              <label for="createTime">属性分类：</label>
            </h-col>
            <h-col :span="3">
              <h-select allowClear mode="multiple" v-model:value="searchParam.labelIds" style="width: 100%">
                <h-select-option v-for="item in plainOptions.filter((v:any)=>{return v?.labelType == 0})" :value="item.id">{{
                  item.labelName
                }}</h-select-option>
              </h-select>
            </h-col>
            <h-col :span="2" style="text-align: right; padding-right: 10px">
              <label for="createTime">业务分类：</label>
            </h-col>
            <h-col :span="3">
              <h-select allowClear mode="multiple" v-model:value="searchParam.labelIds2" style="width: 100%">
                <h-select-option v-for="item in plainOptions.filter((v:any)=>{return v?.labelType == 2})" :value="item.id">{{
                  item.labelName
                }}</h-select-option>
              </h-select>
            </h-col>
            <h-col :span="2" style="text-align: right; padding-right: 10px">
              <label for="createTime">其他标签：</label>
            </h-col>
            <h-col :span="3">
              <h-select allowClear mode="multiple" v-model:value="searchParam.labelIds1" style="width: 100%">
                <h-select-option v-for="item in plainOptions.filter((v:any)=>{return v?.labelType == 1})" :value="item.id">{{
                  item.labelName
                }}</h-select-option>
              </h-select>
            </h-col>
            <h-col :span="9" style="text-align: right">
              <h-button style="margin-right: 10px" @click="reset">重置</h-button>
              <h-button style="margin-right: 10px" type="primary" @click="handleTableChange({ current: 1, pageSize: 9999 })">
                <SearchOutlined />查询
              </h-button>
              <a-badge :count="selectIndexArray.length">
                <h-button  type="primary"  @click="goApply">
                  提交
                </h-button>
              </a-badge>
              <h-button style="margin-left: 20px" type="primary" @click="openHelpModal=true">
                <QuestionCircleOutlined />帮助
              </h-button>
            </h-col>
          </h-row>
        </h-col>
      </h-row>
    </a-card>
  <!-- 帮助弹窗 -->
  <a-modal :width="1200" v-model:open="openHelpModal" title="操作指引"  :footer="null">
    <video controls v-if="openHelpModal" autoplay width="1152" height="550">
      <source src="https://travel-test.haier.net/hbweb/file/download/obs-swszh1/1714372121-iHaier20240429-142510.mp4" type="video/mp4" />
    </video>
  </a-modal>
    <!-- 指标列表 -->
    <div v-loading="loading" class="indexListBox">
      <!-- 指标item -->
      <div v-for="(item, index) in dataSource" :key="item.id" class="indexItemBox">
        <div class="indexItemHeader">
          <div class="headerLeftBox">
            <a-checkbox  @change="(value) =>indexChange(value,item)" style="margin-right:10px;" :checked="isChecked(item.id)"></a-checkbox>
          </div>
          <div class="reportName">
            {{ item.reportName }}
            <a-popover title="指标详情" trigger="click">
            <template #content>
                <span style="margin-top:10px;display: block;">1.指标定义</span>
                <div style="max-width:800px;margin-top:3px;white-space: pre-line;">{{item.knowCenterVo.entryName}} : {{item.knowCenterVo.entryContent}}</div>
                <span style="margin-top:10px;display: block;">2.其他描述</span>
                <div style="width:500px;margin-top:3px;white-space: pre-line;">{{item.indexReportContent}}</div>
              </template>
              <QuestionCircleOutlined style="color: #7f7f7f" />
            </a-popover>
          </div>
          <div class="headerRight">
            <h-tag style="margin-inline-end:0px;" v-for="(v, index) in item.labelVo" v-show="index < 1" color="#1677ff">{{ v.labelName }}</h-tag>
            <h-popover title="更多标签">
              <template #content>
              <h-tag v-for="(v, index) in item.labelVo" v-show="index >= 1" color="#1677ff">{{ v.labelName }}</h-tag>
              </template>
              <MenuOutlined  v-if="item.labelVo.length>=2" style="color: #1677ff;margin-left:10px;"/>
            </h-popover>
          </div>
        </div>
        <div class="indexItemBottom">
          <!-- <Echarts v-if="item.dataSearch==0" :queryCondition="item.queryCondition" :echartsJson="item.echartsJson" :dataType="item.dataSearch" :height="24" :id="index" />
          <Rank  v-if="item.dataSearch==2" :queryCondition="item.queryCondition" :echartsJson="item.echartsJson" :height="24"></Rank> -->
          <img style="width: 100%; height: 100%" :src="item.imagesUrl" alt="" />
        </div>
      </div>
    </div>
    <!-- <div class="pagination">
        <a-pagination v-model:current="current" v-model:pageSize="pageSize" :total="data?.total" show-less-items />
    </div> -->
    <!-- 悬浮 -->
    <h-float-button @click="goApply" shape="circle" :badge="{  count: selectIndexArray.length  }" :style="{ right: '40px',width:'60px',height:'60px',bottom:'150px' }" >
      <template #icon>
        <ShoppingCartOutlined style="font-size:26px;color:#1677ff" />
      </template>
    </h-float-button>
    <div v-if="visible">
      <edit-dialog :show="visible" :data="selectIndexArray" @remove="removeReportItem" @cancel="onDialogClose">
      </edit-dialog>
    </div>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.indexListBox {
  min-height:calc(100vh - 300px);
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  .indexItemBox {
    display: flex;
    flex-flow: column;
    width: 32.5%;
    min-height: 30vh;
    height: 30vh;
    background: inherit;
    background-color: rgba(255, 255, 255, 1);
    border: none;
    border-radius: 0px;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    margin: 12px 1.2% 0 0;
    &:nth-child(3n) {
      margin-right: 0;
    }
    .indexItemBottom {
      flex: 1;
      height: calc(30vh - 48px);
      padding: 10px 16px;
    }
    .indexItemHeader {
      width: 100%;
      position: relative;
      height: 48px;
      background: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 10px;
      border-bottom: 1px solid #ccc;
      .reportName {
        text-align: center;
        font-weight: 900;
        font-size: 14px;
        color: #000;
        position: relative;
        z-index: 99;
      }
      .headerLeftBox {
        position: absolute;
        left:20px;
        width: 10%;
        cursor: pointer;
      }
      .headerRight {
        position: absolute;
        right:20px;
        width: 35%;
        text-align: right;
        z-index: 10;
      }
    }
  }
}
.pagination{
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top:20px;
}
:deep(.el-loading-mask){
  background-color: rgba(255,255,255,0.5);
}
.ant-popover-inner-content{
  span{
    font-weight: 600;
    font-size: 12px;
  }
  div{
    font-size: 12px;
  }
}
:deep(.ant-float-btn-icon){
  width: 26px !important;
}
</style>
