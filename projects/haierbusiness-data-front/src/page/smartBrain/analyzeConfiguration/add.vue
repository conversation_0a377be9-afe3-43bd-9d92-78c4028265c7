<script setup lang="ts">
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { smartBrainApi } from '@haierbusiness-front/apis';
import { getCurrentRouter, routerParam } from '@haierbusiness-front/utils';
import { ISmartBrainFilter, ISmartBrain, IndexList, ISetting } from '@haierbusiness-front/common-libs';
import { PlusOutlined, SearchOutlined, EditOutlined, SwapOutlined, CloseOutlined, QuestionCircleOutlined, FormOutlined,AreaChartOutlined, FundOutlined } from '@ant-design/icons-vue';
import Editor from '@haierbusiness-front/components/editor/Editor.vue'
import type { IDomEditor } from "@wangeditor/editor"
import { CSSProperties, reactive } from 'vue';
import { ref, watch, onMounted, computed, onUnmounted } from 'vue';
import { message, Dropdown as hDropdown } from 'ant-design-vue';
const router = getCurrentRouter();
import { useRoute } from 'vue-router';
const route = useRoute();
const saveForm = reactive<ISetting>({});
// 定义变量
const managerName = ref<string>('');
// 拖拽布局
const layout = ref<any>([]);
// ref
const gridlayout = ref<any>(null);
const gridItemRefs = ref<any>([]);
// 计算坐标用
let mouseXY:any = { x: null, y: null };
let DragPos:any = { x: null, y: null, w: 1, h: 1, i: null };
// 指标申请列表
const indexApplicationList = ref<IndexList[]>([]);
// 求单个单元格的高度
const gridItemHeight = ref<number>(240);
const SearchName = ref<string>('')
const visible = ref<boolean>(false);
const handleDrop = (event: any) => {
  event.preventDefault();
  // TODO
};
const activeKey =ref<string>("1")
const uploadUrl = import.meta.env.VITE_UPLOAD_URL
const openHelpModal = ref<boolean>(false);

// 弹窗编辑
const editComponentsShow = ref<boolean>(false);

// 添加指标
const addItem = (item: any) => {
  // Add a new item. It must have a unique key!
  let obj = {
    x: (layout.value.length * 2) % 3,
    y: layout.value.length + 3, // puts it at the bottom
    w: 1,
    h: 1,
    i: item.tid,
  };
  Object.assign(obj, item);
  layout.value.push(obj);
};

const editContent = ref('')

// 计算坐标
const calcXY = (el: any, top: number, left: number, width: number | string, height: number | string) => {
  const colWidth = el.calcColWidth();
  let x = Math.round((left - 10) / (colWidth + 10));
  let y = Math.round((top - 10) / (el.rowHeight + 10));
  const _width = width || el.innerW;
  const _height = height || el.innerH;
  x = Math.max(Math.min(x, el.cols - _width), 0);
  y = Math.max(Math.min(y, el.maxRows - _height), 0);
  return { x, y };
};

const tidArray = ref([])

watch(layout, (newList, oldList) => {
  tidArray.value = newList.map((item:any) => {
    return item.tid
  });
}, {
  deep: true, // 监听数组中对象的变化
});

// 开始拖动
const drag = (e: any) => {
  let parentRect = document.getElementById('content').getBoundingClientRect();
  console.log(parentRect);
  let mouseInGrid = false;
  if (
    mouseXY.x > parentRect.left &&
    mouseXY.x < parentRect.right &&
    mouseXY.y > parentRect.top &&
    mouseXY.y < parentRect.bottom
  ) {
    mouseInGrid = true;
  }

  if (mouseInGrid === true && layout.value.findIndex((item: any) => item.i === 'drop') === -1) {
    layout.value.push({
      x: (layout.value.length * 2) % 3,
      y: layout.value.length + 3, // puts it at the bottom
      w: 1,
      h: 1,
      i: 'drop',
    });
  }
  let index = layout.value.findIndex((item: any) => item.i === 'drop');
  if (index !== -1) {
    // try {
    //   gridItemRefs.value[layout.value.length - 1].$refs.item.style.display = 'none';
    // } catch {}
    let el = gridItemRefs.value[index];
    // el.dragging = { 'top': mouseXY.y - parentRect.top, 'left': mouseXY.x - parentRect.left };
    let new_pos = calcXY(el, mouseXY.y - parentRect.top, mouseXY.x - parentRect.left, 1, 1);
    if (mouseInGrid === true) {
      // 红色游标部分
      gridlayout.value.dragEvent('dragstart', 'drop', new_pos.x, new_pos.y, DragPos?.h || 1, DragPos?.w || 1);
      DragPos.i = String(Date.now());
      DragPos.x = layout.value[index].x;
      DragPos.y = layout.value[index].y;
    }
    if (mouseInGrid === false) {
      gridlayout.value.dragEvent('dragend', 'drop', new_pos.x, new_pos.y, 1, 1);
      layout.value = layout.value.filter((obj: any) => obj.i !== 'drop');
    }
  }
};

// 释放拖动
const dragend = (item: any) => {
  console.log(item, '---------------------');
  let parentRect = document.getElementById('content').getBoundingClientRect();
  let mouseInGrid = false;
  if (
    mouseXY.x > parentRect.left &&
    mouseXY.x < parentRect.right &&
    mouseXY.y > parentRect.top &&
    mouseXY.y < parentRect.bottom
  ) {
    mouseInGrid = true;
  }
  if (mouseInGrid === true) {
    gridlayout.value.dragEvent('dragend', 'drop', DragPos.x, DragPos.y, 1, 1);
    let obj = JSON.parse(JSON.stringify(DragPos));
    Object.assign(obj, item);
    layout.value.push(obj);
    // gridConfig.addGridItem(DragPos)
    layout.value = layout.value.filter((obj: any) => obj.i !== 'drop');
  }
  gridItemRefs.value[layout.value.length - 1].$refs.item.style.display = 'none';
};

// 请求指标list列表
const BrainReportLis = (userId?:string,searchKey?:string) => {
  smartBrainApi.getOwnerBrainReportList({userId,searchKey}).then((res: Array<IndexList>) => {
    console.log(res);
    indexApplicationList.value = res;
  });
};

// 改变大小尺寸
const changeSize = (type: string, item: any) => {
  if (type == '1-2') {
    item.w = 2;
    item.h = 1;
  }
  if (type == '1-3') {
    item.w = 3;
    item.h = 1;
  }
  if (type == '1-1') {
    item.w = 1;
    item.h = 1;
  }
  if (type == '2-2') {
    item.w = 2;
    item.h = 2;
  }
  if (type == '2-3') {
    item.w = 3;
    item.h = 2;
  }
};

// 获取指标详情
const getBrainReportManagerVoById = () => {
  smartBrainApi.getBrainReportManagerVoById({ id: route.query.id }).then((res: any) => {
    saveForm.managerName = res.managerName;
    layout.value = JSON.parse(res.baseManageInfo);
  });
};

// 删除指标
const deleteGridItem = (item: any) => {
  let n = '';
  layout.value.forEach((v: any, index: number) => {
    if (v.i == item.i) {
      n = index;
    }
  });
  layout.value.splice(n, 1);
};

// 保存指标
const save = () => {
  if(!saveForm.managerName){
     message.error(`请输入视图名称`);
     return
  }
  let array = [];
  layout.value.forEach((item) => {
    if(item.name!='title'){
      array.push(item.tid);
    }
  });
  saveForm.echartsId = array.toString();
  saveForm.baseManageInfo = JSON.stringify(layout.value);
  if (route.query.id) {
    saveForm.id = route.query.id;
  }
  if(route.query.userId){
    saveForm.userId = route.query.userId;
  }
  smartBrainApi.managerCreateOrUpdate(saveForm).then((res: any) => {
    message.success(`保存成功`);
    router.push({ path: '/data/smartBrain/analyzeConfiguration',query:{form:'add'} });
  });
};


const editData = ref<any>({})

// 编辑小组件内容
const editComponents=(item:any)=>{
  console.log(item,"************************")
  editData.value = item
  editContent.value = item.content
  editComponentsShow.value = true
}


const onEditorChange = (editor: IDomEditor) => {
  editContent.value = editor.getHtml()
}

const saveCompontent =()=>{
  editData.value.content = editContent.value
  editComponentsShow.value = false
}

// 加载完成后监听鼠标事件
onMounted(() => {
  // 获取可拖拽的申请的指标
  if (route.query.id) {
    getBrainReportManagerVoById();
  }
  BrainReportLis(route.query.userId);
  document.getElementById('content')?.addEventListener(
    'dragover',
    function (e) {
      e.preventDefault();
      mouseXY.x = e.clientX;
      mouseXY.y = e.clientY;
      console.log('-------------------', mouseXY);
    },
    false,
  );
  gridItemHeight.value = (document.getElementById('content')?.offsetWidth / 12) * 0.5;
  // console.log(document.querySelector('.grid').style,"*//////")
  // document.querySelector('.grid').style.setProperty('before', 'background-size:calc(calc(100% - 5px) / 3) '+gridItemHeight.value+'px')
  window.onresize = () => {
    return (() => {
      gridItemHeight.value = (document.getElementById('content')?.offsetWidth / 12) * 0.5;
  // document.querySelector('.grid').style.setProperty('before', 'background-size:calc(calc(100% - 5px) / 3) '+gridItemHeight.value+'px')
    })();
  };
});

// 销毁时关闭监听
onUnmounted(() => {
  document.removeEventListener(
    'dragover',
    function (e) {
      mouseXY.x = null;
      mouseXY.y = null;
    },
    false,
  );
});

// 定义样式
const contentStyle: CSSProperties = {
  textAlign: 'center',
  minHeight: 'calc(100vh - 170px)',
  height: 'calc(100vh - 170px)',
  color: '#fff',
  backgroundColor: '#fff',
  border: '0px solid #1677ff',
  overflow: 'auto',
};

const siderStyle: CSSProperties = {
  textAlign: 'center',
  minHeight: 'calc(100vh - 170px)',
  height: 'calc(100vh - 170px)',
  color: '#fff',
  backgroundColor: '#fff!important',
  padding: '10px 0 10px 0',
  overflow: 'auto',
  border: '0px solid #1677ff',
  margin: '0 10px 0 0',
};

const searchByName = () =>{
  BrainReportLis(route.query.userId,SearchName.value)
}
</script>

<template>
  <div style="height: 100%; width: 100%; padding: 0px 10px 0px 10px; overflow: auto">
    <!-- 布局主页面 -->
    <a-layout>
      <a-layout-header class="headerBox">
        <a-row style="width: 100%" :align="'middle'">
          <a-col :span="1.5" class="label" style="text-align: right; padding-right: 10px">
            <label for="managerName">视图名称：</label>
          </a-col>
          <a-col :span="4">
            <a-input
              id="sqr"
              v-model:value="saveForm.managerName"
              placeholder="请输入视图名称"
              autocomplete="off"
              :maxlength="50"
              allow-clear
            />
          </a-col>
        </a-row>
        <a-button style="margin-right: 10px" type="primary" @click="openHelpModal=true">
          <QuestionCircleOutlined />帮助
        </a-button>
        <a-button @click="save" type="primary">保存</a-button>
      </a-layout-header>
      <a-layout>
        <a-layout-sider :style="siderStyle" :width="250">
          <a-tabs :tabBarGutter="50"  v-model:activeKey="activeKey">
            <a-tab-pane key="1">
              <template #tab>
                <span>
                  <AreaChartOutlined />指标
                </span>
              </template>
              <div class="titleBox">
                <div class="title">指标列表</div>
                <a-popover title="名称检索" placement="bottom" trigger="click">
                    <template #content>
                        <a-input-search v-model:value="SearchName" enter-button allow-clear @search="searchByName"
                            autocomplete="off" />
                    </template>
                    <a-button type="primary" shape="circle" style="float: right;margin-bottom:10px;">
                        <template #icon>
                            <SearchOutlined />
                        </template>
                    </a-button>
                </a-popover>
              </div>
          <div v-for="item in indexApplicationList" :style="{'pointer-events':tidArray.includes(item.tid)?'none':''}" :key="item.index" class="reportBox" @drag="drag" @dragend="dragend(item)" :draggable="true" unselectable="on">
            <!-- <a-popover placement="right" title="点击添加指标">
              <template #content>
                <img :src="item.imagesUrl" alt="">
              </template> -->
            <div @click="addItem(item)"  class="report">
              <span :style="{'color':tidArray.includes(item.tid)?'#ccc':'#000'}">{{
                item.reportName
              }}</span>
              <transition name="fade">
                <div class="imgBox"><img :src="item.imagesUrl" alt="" /></div>
              </transition>
            </div>
            <!-- </a-popover> -->
          </div>
            </a-tab-pane>
            <a-tab-pane key="2"  force-render>
              <template #tab>
                <span>
                  <FundOutlined />组件
                </span>
              </template>
              <div class="titleBox">
                <div class="title">组件列表</div>
              </div>
              <div @drag="drag" @dragend="dragend({name:'title'})" :draggable="true" unselectable="on" class="report">
                富文本小组件
              </div>
            </a-tab-pane>
          </a-tabs>
        </a-layout-sider>
        <a-layout-content :style="contentStyle" id="content">
          <grid-layout
            class="grid"
            id="gridlayout"
            ref="gridlayout"
            :layout.sync="layout"
            :col-num="3"
            :row-height="gridItemHeight"
            :is-draggable="true"
            :is-resizable="true"
            :is-mirrored="false"
            :vertical-compact="true"
            :margin="[12, 12]"
            :use-css-transforms="true"
            :preventCollision="false"
            :responsive="false"
            :useStyleCursor="false"
          >
            <grid-item
              v-for="(item, index) in layout"
              ref="gridItemRefs"
              :x="item.x"
              :y="item.y"
              :w="item.w"
              :h="item.h"
              :i="item.i"
              :key="item.i"
              style="
                display: flex;
                flex-flow: column;
                background: #fff;
              "
            >
            <div style="overflow:hidden;height: 100%;" v-if="item.name!='title'">
              <div class="header">
                <div></div>
                <div class="reportName">{{ item.reportName }}</div>
                <div v-if="item.imagesUrl" class="operation">
                  <!-- <a-dropdown :key="item.i"> -->
                  <div class="linkIcon">
                    <SwapOutlined style="margin: 0 10px" />
                    <div class="dropDown">
                      <ul>
                        <li @click="changeSize('1-1', item)">1:1</li>
                        <li @click="changeSize('1-2', item)">1:2</li>
                        <li @click="changeSize('1-3', item)">1:3</li>
                        <li @click="changeSize('2-2', item)">2:2</li>
                        <li @click="changeSize('2-3', item)">2:3</li>
                      </ul>
                    </div>
                  </div>
                  <a @click="deleteGridItem(item)" class="ant-dropdown-link" @click.prevent
                    ><CloseOutlined style="margin: 0 10px"
                  /></a>
                </div>
              </div>
              <div class="bottom">
                <img v-if="item.imagesUrl" style="height: 100%" :src="item.imagesUrl" alt="" />
              </div>
            </div>
            <div class="itemBox" v-else>
              <div class="header editHeader">
                <div></div>
                <div  class="operation">
                  <!-- <a-dropdown :key="item.i"> -->
                  <div class="linkIcon">
                    <FormOutlined @click="editComponents(item)" style="margin: 0 10px" />
                  </div>
                  <a @click="deleteGridItem(item)" class="ant-dropdown-link" @click.prevent
                    ><CloseOutlined style="margin: 0 10px"
                  /></a>
                </div>
              </div>
              <div class="contentBox" v-html="item.content">
                
              </div>
            </div>
            </grid-item>
          </grid-layout>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
  <!-- 帮助弹窗 -->
  <a-modal :width="1200" v-model:open="openHelpModal" title="操作指引"  :footer="null">
    <video controls v-if="openHelpModal" autoplay width="1152" height="550">
      <source src="https://travel-test.haier.net/hbweb/file/download/obs-swszh1/1714372451-分析帮助.mp4" type="video/mp4" />
    </video>
  </a-modal>
  <!-- 小组件编辑弹窗 -->
  <a-modal :width="1200" v-model:open="editComponentsShow" title="修改小组件内容" @ok="saveCompontent">
    <editor height="300px" :modelValue="editContent" @change="onEditorChange" style="z-index: 20" :uploadUrl="uploadUrl" />
  </a-modal>
</template>

<style scoped lang="less">

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
.h-manage .ant-layout-sider {
  background: '#fff';
  // border:1px solid #1677ff;
}

.headerBox {
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: auto;
  line-height: 50px;
  padding-right: 10px;
  padding-left: 10px;
  margin-bottom: 10px;
}
.title {
  color: #000;
  font-weight: 900;
  font-size: 16px;
  padding-bottom: 8px;
}
.reportBox {
  position: relative;
}
.report {
  transition-delay: 2s; /* 在3秒后开始过渡效果 */
  color: #000;
  margin-top: 10px;
  cursor: pointer;
  text-align: left;
  padding: 8px 0 8px 24px;
  .imgBox {
    width: 90%;
    display: none;
    margin: 10px 0 0 0;
    box-shadow: 0 1px 1px hsl(0deg 0% 0% / 0.075), 0 2px 2px hsl(0deg 0% 0% / 0.075), 0 4px 4px hsl(0deg 0% 0% / 0.075),
      0 8px 8px hsl(0deg 0% 0% / 0.075), 0 16px 16px hsl(0deg 0% 0% / 0.075);
    img {
      width: 100%;
    }
  }
  &:hover {
    color: #1677ff;
    .imgBox {
      display: block;
    }
  }
}
.header {
  width: 100%;
  height: 48px;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  .reportName {
    color: #000;
    font-weight: 900;
  }
  .operation {
    display: flex;
  }
}
.itemBox{
  position: relative;
  height: 100%;
  // overflow: auto; 
  .contentBox{
    // background: #fff;
    height: 100%;
    overflow: auto;
    color:#000;
    text-align: start;
    padding: 6px;
    overflow: auto;
  }
}
.editHeader {
  height: 32px;
  position:absolute;
  top:0;
  right:0;
  width:auto;
  background:rgba(255, 255, 255, 0.9)
}

.bottom {
  flex: 1;
  width: 100%;
  padding: 0 5px 5px 5px;
  background: #fff;
  overflow:hidden;
  height: 100%;
}
/* 定义过渡样式 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 5s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
.linkIcon {
  color: #1677ff;
  position: relative;
  .dropDown {
    display: none;
    position: absolute;
    background: #fff;
    border-radius: 10px;
    right: -25px;
    box-shadow: 0 1px 1px hsl(0deg 0% 0% / 0.075), 0 2px 2px hsl(0deg 0% 0% / 0.075), 0 4px 4px hsl(0deg 0% 0% / 0.075),
      0 8px 8px hsl(0deg 0% 0% / 0.075), 0 16px 16px hsl(0deg 0% 0% / 0.075);
    ul,
    li {
      width: 80px;
      padding: 5px 0;
      text-align: center;
      // background: #fff;
      cursor: pointer;
      margin-bottom: 0;
    }
  }
  &:hover {
    .dropDown {
      display: block;
    }
  }
}
.titleBox{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  border-bottom: 0.5px solid #f1f1f5;
}
.label::before {
    display: inline-block;
    margin-inline-end: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*";
}
.grid{
  min-height: 100%;
  position: relative;
  background: #f5f5f5
}
// .grid::before {
//     content: '';
//     // background-size: calc(calc(100% - 5px) / 3)  25vh;
//     background-image: linear-gradient(90deg, rgba(60, 10, 30, 0.1) 1px, transparent 0),
//   linear-gradient(1turn, rgba(60, 10, 30, 0.1) 1px, transparent 0);
//     height: calc(100% - 5px);
//     width: calc(100% - 5px);
//     position: absolute;
//     background-repeat: repeat;
//     margin:5px;
//     left:0;
// }
ol, ul {
    /* margin: 0; */
    padding: revert-layer;
    list-style: auto;
}
:deep(.ant-tabs-nav-wrap){
    padding: 0 40px;
}
</style>
