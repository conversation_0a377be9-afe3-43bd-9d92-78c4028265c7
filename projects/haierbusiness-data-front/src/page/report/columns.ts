import { checkUserGroups } from '@haierbusiness-front/utils/src/authorityUtil'
import { UserGroupSystemConstant } from '@haierbusiness-front/common-libs';

export const bookingColumns = [
    {
        alias: "订单号",
        width: "200px",
        column: ["order_code"],
    },
    {
        alias: "订单状态",
        column: ["order_state"],
    },
    {
        alias: "订单创建时间",
        width: "200px",
        column: ["order_create_datetime"],
    },
    {
        alias: "酒店编号",
        column: ["order_hotel_id"],
    },
    {
        alias: "酒店名称",
        width: "200px",
        column: ["order_hotel_name"],
    },
    {
        alias: "预定人工号",
        column: ["order_applicant_id_filter"],
    },
    {
        alias: "预定人姓名",
        column: ["order_applicant_name_filter"],
    }, {
        alias: "预定人部门",
        column: ["order_applicant_department"],
    },
    {
        alias: "业务申请人",
        column: ["order_owner_id"],
    },
    {
        alias: "有效签单人",
        column: ["order_signer_name"],
    },
    {
        alias: "房间数量",
        column: ["order_room_num"],
    },
    {
        alias: "来宾单位",
        column: ["order_guest_company"],
    },
    {
        alias: "支付方式",
        column: ["order_pay_type"],
    },
    {
        alias: "入住日期",
        width: "200px",
        column: ["order_check_in_datetime"],
    },
    {
        alias: "离店日期",
        width: "200px",
        column: ["order_check_out_datetime"],
    },
    {
        alias: "合计间晚数",
        column: ["room_nights"],
    },
    {
        alias: "市场金额",
        column: ["order_public_amount"],
    },
    {
        alias: "节省金额",
        column: ["discount_amount"],
    },
    {
        alias: "合计金额",
        column: ["order_actual_amount"],
    },
    {
        alias: "直线审批人",
        column: ["direct_manager_name_filter"],
    },
    {
        alias: "直线审批状态",
        column: ["direct_manager_audit_state"],
    },
    {
        alias: "直线审批时间",
        width: "200px",
        column: ["direct_manager_audit_date"],
    },
    {
        alias: "二线审批人",
        column: ["indirect_manager_name_filter"],
    },
    {
        alias: "二线审批状态",
        column: ["indirect_manager_audit_state"],
    },
    {
        alias: "二线审批时间",
        width: "200px",
        column: ["indirect_manager_audit_date"],
    },
    {
        alias: "预约管理员锁单状态",
        width: "150px",
        column: ["order_is_locked"],
    },
    {
        alias: "锁单时间",
        width: "200px",
        column: ["order_locked_datetime"],
    },
    {
        alias: "预算人工号",
        column: ["budgeter_id"],
    },
    {
        alias: "预算人姓名",
        column: ["budgeter_name"],
    },
    {
        alias: "预算部门名称",
        width: "200px",
        column: ["budget_department_name"],
    },
    {
        alias: "预算单号",
        width: "200px",
        column: ["budget_order_code"],
    },
    {
        alias: "预算类型",
        column: ["budget_source"],
    },
    {
        alias: "提交账单时间",
        column: ["bill_create_datetime"],
    },
    {
        alias: "结算金额",
        column: ["account_total_amount"],
    },
    {
        alias: "账单核对状态",
        column: ["bill_state"],
    },
    {
        alias: "账单核对时间",
        column: ["bill_check_datetime"],
    },
    {
        alias: "发票状态",
        column: ["account_invoice_state"],
    },
    {
        alias: "发票上传时间",
        column: ["account_invoice_issue_datetime"],
    },
    {
        alias: "发票号",
        column: ["account_invoice_no"],
    },
    {
        alias: "供应商编码",
        column: ["order_provider_code"],
    },
    {
        alias: "结算单位编码",
        column: ["account_company_code"],
    },
    {
        alias: "结算单位名称",
        width: "250px",
        column: ["account_company_name"],
    },
    {
        alias: "领域名称",
        width: "250px",
        column: ["field_name"],
    },
    {
        alias: "平台",
        width: "250px",
        column: ["pt_name"],
    },
    {
        alias: "产业线",
        width: "250px",
        column: ["pl_name"],
    },
    {
        alias: "汇总单生成状态",
        column: ["acccount_state"],
    },
    {
        alias: "汇总单号",
        width: "200px",
        column: ["account_code"],
    },
    {
        alias: "汇总单生成时间",
        width: "200px",
        column: ["account_create_datetime"],
    },
    {
        alias: "汇总单生成人",
        column: ["account_create_by"],
    },
    {
        alias: "付款完成时间",
        column: ["payment_success_time"],
    },
];
//火车票报表字段
export const trainColumns = [
    {
        alias: "订单编号",
        width: "200px",
        column: ["ddbh"],
    },
    {
        alias: "订单类型",
        width: "150px",
        column: ["ddlxmc"],
    },
    {
        alias: "原订单号",
        width: "200px",
        column: ["yddbh"],
    },
    {
        alias: "供应商订单号",
        width: "200px",
        column: ["gys_ddbh"],
    },
    {
        alias: "商互通单号",
        column: ["shtdh"],
    },
    {
        alias: "预订时间",
        width: "200px",
        column: ["ydsj"],
    },
    {
        alias: "业务发生时间",
        width: "200px",
        column: ["ywfssj"],
    },
    {
        alias: "支付完成时间",
        width: "200px",
        column: ["zfwcsj"],
    },
    {
        alias: "改期时间",
        width: "200px",
        column: ["gqsj"],
    },
    {
        alias: "退票时间",
        width: "200px",
        column: ["tpsj"],
    },
    {
        alias: "票面票号",
        width: "200px",
        column: ["tkno"],
    },
    {
        alias: "取票单号",
        column: ["qpdh"],
    },
    {
        alias: "创建人工号",
        column: ["sqrgh"],
    },
    {
        alias: "创建人",
        column: ["sqr"],
    },
    {
        alias: "乘车人",
        column: ["cxr_xm_filter"],
    }, {
        alias: "乘车人工号",
        column: ["cxr_gh"],
    }, {
        alias: "乘车人部门名称",
        column: ["cxr_bmmc"],
    },
    {
        alias: "坐席等级",
        column: ["zwlxmc"],
    },
    {
        alias: "出发时间",
        width: "200px",
        column: ["cfsj_filter"],
    },
    {
        alias: "到达时间",
        width: "200px",
        column: ["ddsj_filter"],
    },
    {
        alias: "车次",
        column: ["cch_filter"],
    },
    {
        alias: "出发站",
        column: ["cfzmc_filter"],
    },
    {
        alias: "到达站",
        column: ["ddzmc_filter"],
    },
    {
        alias: "出发城市",
        column: ["cfcsmc_filter"],
    },
    {
        alias: "到达城市",
        column: ["ddcsmc_filter"],
    },
    {
        alias: "订单状态",
        column: ["ddztmc"],
    },
    {
        alias: "是否被改签",
        column: ["sfgq_state"],
    },
    {
        alias: "是否退票",
        column: ["sftp_state"],
    },
    {
        alias: "票面价",
        column: ["jg_pj"],
    },
    {
        alias: "改期费",
        column: ["gqsxf"],
    },
    {
        alias: "退票费",
        column: ["tp_tpsxf"],
    },
    {
        alias: "服务费",
        column: ["ptfwf"],
    },
    {
        alias: "保险费",
        column: ["bxje"],
    },
    {
        alias: "实收/实退",
        column: ["fyje"],
    }, {
        alias: "超标金额",
        column: ["cbje"],
    },
    {
        alias: "支付类型",
        column: ["pay_type"],
    },
    {
        alias: "支付科目",
        column: ["zf_zfkmmc"],
    },
    {
        alias: "预算人工号",
        column: ["budget_people_emp_num"],
    },
    {
        alias: "预算人姓名",
        column: ["budget_people_name"],
    },
    {
        alias: "预算类型",
        column: ["budget_source"],
    },
    {
        alias: "预算单号",
        width: "200px",
        column: ["ysdh"],
    },
    {
        alias: "预算部门编码",
        width: "200px",
        column: ["budget_department_code"],
    },
    {
        alias: "预算部门名称",
        width: "200px",
        column: ["budget_department_name"],
    },
    {
        alias: "企业简称",
        width: "200px",
        column: ["qyjc"],
    },
    {
        alias: "费用项目编码",
        column: ["yskm"],
    },
    {
        alias: "费用项目名称",
        column: ["yskmmc"],
    },
    {
        alias: "项目号",
        column: ["xmdh"],
    },
    {
        alias: "研发项目编码",
        column: ["xmid"],
    },
    {
        alias: "研发项目名称",
        column: ["xmmc"],
    },
    {
        alias: "结算单位编码",
        column: ["account_company_code"],
    },
    {
        alias: "结算单位名称",
        width: "250px",
        column: ["account_company_name"],
    },
    {
        alias: "领域名称",
        width: "250px",
        column: ["field_name"],
    },
    {
        alias: "平台",
        width: "250px",
        column: ["pt_name"],
    },
    {
        alias: "产业线",
        width: "250px",
        column: ["pl_name"],
    },
    {
        alias: "结算单号",
        column: ["jsdh"],
    },
    {
        alias: "出差申请单号",
        width: "200px",
        column: ["ccsqd"],
    },
    {
        alias: "申请单类型",
        width: "200px",
        column: ["sqdlxmc"],
    },
    {
        alias: "差标原则",
        column: ["sfwbmc"],
    },
    {
        alias: "汇总单状态",
        column: ["hzd_tszt"],
    },
    {
        alias: "报账单号",
        width: "200px",
        column: ["hzd_id"],
    },
    {
        alias: "汇总单生成时间",
        width: "200px",
        column: ["hzd_bzrq"],
    },
    {
        alias: "汇总单生成人",
        column: ["hzd_sqrxm"],
    },
    {
        alias: "付款完成时间",
        column: ["hzd_tssj"],
    }, {
        alias: "数据来源",
        column: ["sjly"],
    }
];
//国内机票
export const internalColumns = [

    {
        alias: "订单类型",
        width: "150px",
        column: ["ddlxmc"],
    },
    {
        alias: "是否退票",
        column: ["sftp_state"],
    },
    {
        alias: "机票票号",
        width: "150px",
        column: ["tkno"],
    },
    {
        alias: "原票号",
        width: "150px",
        column: ["ytkno"],
    },
    {
        alias: "结算单号",
        column: ["jsdh"],
    },{
        alias: "报账单号",
        width: "200px",
        column: ["hzd_id"],
    },
    {
        alias: "商互通单号",
        column: ["shtdh"],
    }, {
        alias: "实收/实退",
        column: ["fyje"],
    },  {
        alias: "业务发生时间",
        width: "200px",
        column: ["ywfssj"],
    },
    {
        alias: "是否会展订单",
        column: ["mice_flag_mc"],
    }, {
        alias: "企业简称",
        width: "200px",
        column: ["qyjc"],
    }, {
        alias: "公布运价",
        column: ["bzzdj"],
    },
    {
        alias: "供应商",
        column: ["supplier_name"],
    }, {
        alias: "政策节省",
        column: ["zcjs"],
    },  {
        alias: "法人公司编号",
        column: ["account_company_code"],
    },
    {
        alias: "法人公司",
        width: "250px",
        column: ["account_company_name"],
    },

    {
        alias: "领域名称",
        width: "250px",
        column: ["field_name"],
    },
    {
        alias: "平台",
        width: "250px",
        column: ["pt_name"],
    },
    {
        alias: "产业线",
        width: "250px",
        column: ["pl_name"],
    },
    {
        alias: "团队申请单号",
        width: "200px",
        column: ["tdsqdh"],
    },
    {
        alias: "订单编号",
        width: "200px",
        column: ["ddbh"],
    },
    {
        alias: "原订单编号",
        width: "200px",
        column: ["yddbh"],
    },
    {
        alias: "供应订单编号",
        width: "200px",
        column: ["cgptdh"],
    },
    {
        alias: "创建人工号",
        column: ["sqrgh_filter"],
    },
    {
        alias: "创建人",
        column: ["sqr_filter"],
    },
    {
        alias: "客票状态",
        column: ["jpzt"],
    },
    {
        alias: "机票状态",
        column: ["jpztmc"],
    },
    {
        alias: "行程单号",
        column: ["xcdh"],
    },
    {
        alias: "预订时间",
        width: "200px",
        column: ["ydsj"],
    },
    {
        alias: "改期时间",
        width: "200px",
        column: ["gqsj"],
    },
    {
        alias: "退票时间",
        width: "200px",
        column: ["tpsj"],
    },
    {
        alias: "出票时间",
        width: "200px",
        column: ["cpsj"],
    },
    {
        alias: "订票时间",
        width: "200px",
        column: ["dd_ydsj"],
    },
    {
        alias: "是否改签",
        column: ["sfgq_state"],
    },
    {
        alias: "乘机人",
        column: ["cxr_xm_filter"],
    }, {
        alias: "乘机人工号",
        column: ["cxr_gh_filter"],
    }, {
        alias: "乘机人部门",
        column: ["cxr_bmmc"],
    }, {
        alias: "航程类型",
        column: ["hclx"],
    }, {
        alias: "航程",
        column: ["hc"],
    }, {
        alias: "出发机场",
        column: ["cfzmc"],
    }, {
        alias: "出发机场三字码",
        column: ["cfz"],
    }, {
        alias: "到达机场",
        column: ["ddzmc"],
    }, {
        alias: "到达机场三字码",
        column: ["ddz"],
    }, {
        alias: "提前预订天数",
        column: ["tqydts"],
    },
    // {
    //     "alias": "证件号",
    //     "column": [
    //         "cxr_zjhm"
    //     ]
    // },
    {
        alias: "出发城市",
        column: ["pnr_cfcs_mc_filter"],
    },
    {
        alias: "到达城市",
        column: ["pnr_ddcs_mc_filter"],
    },
    {
        alias: "航空公司",
        column: ["hkgsmc"],
    }, {
        alias: "航班号",
        column: ["pnr_hbh_filter"],
    },
    {
        alias: "折扣",
        column: ["zkl"],
    },{
        alias: "前后N小时最低价",
        column: ["qhjxszdj"],
    },{
        alias: "差旅标准价",
        column: ["clbzj"],
    },{
        alias: "应收金额含服务费",
        column: ["ysjehfwf"],
    },{
        alias: "应结金额含服务费",
        column: ["yjjehfwf"],
    },
    {
        alias: "舱位码",
        column: ["jp_cw"],
    },{
        alias: "舱位类型",
        column: ["cwlx"],
    },
    {
        alias: "出发时间",
        width: "200px",
        column: ["jp_cfsj_filter"],
    },
    {
        alias: "到达时间",
        width: "200px",
        column: ["jp_ddsj_filter"],
    },
    {
        alias: "销售价",
        column: ["jg_xsj"],
    },
    {
        alias: "机建费",
        column: ["jg_jsf"],
    },
    {
        alias: "税费",
        column: ["jg_tax"],
    },
    {
        alias: "升舱费",
        column: ["scf"],
    },
    {
        alias: "改签手续费",
        column: ["gqsxf"],
    },
    {
        alias: "退票手续费费",
        column: ["tp_tpsxf"],
    },
    {
        alias: "超标金额",
        column: ["cbje"],
    },
    {
        alias: "TMC服务费",
        column: ["ptfwf"],
    },
    // {
    //     alias: "TMC服务费",
    //     column: ["fwffws"],
    // },
    {
        alias: "改签后票号",
        column: ["gqhph"],
    },
    {
        alias: "改签费用",
        column: ["gqfy"],
    },
    {
        alias: "退改签标志",
        column: ["sftgmc"],
    },
    {
        alias: "保险单号",
        column: ["zddbh"],
    },

    {
        alias: "保险保单号",
        column: ["bxbdh"],
    },

    {
        alias: "保险状态",
        column: ["bxztmc"],
    },
    {
        alias: "保险金额",
        column: ["bxje"],
    },
    {
        alias: "支付类型",
        column: ["pay_type"],
    },
    {
        alias: "支付科目",
        column: ["zf_zfkmmc"],
    },
    {
        alias: "交易流水号",
        width: "200px",
        column: ["zf_zflsh"],
    },
    {
        alias: "因公因私",
        column: ["clyymc"],
    },
    {
        alias: "国内国际",
        column: ["gngjmc"],
    },
    {
        alias: "供应商编号",
        column: ["gys"],
    },
    {
        alias: "预算人工号",
        column: ["budget_people_emp_num"],
    },
    {
        alias: "预算人姓名",
        column: ["budget_people_name"],
    },
    {
        alias: "预算类型",
        column: ["budget_source"],
    },
    {
        alias: "预算单号",
        width: "200px",
        column: ["ysdh"],
    },
    {
        alias: "预算部门编码",
        width: "200px",
        column: ["budget_department_code"],
    },
    {
        alias: "预算部门名称",
        width: "200px",
        column: ["budget_department_name"],
    },
    {
        alias: "费用项目编码",
        column: ["yskm"],
    },
    {
        alias: "费用项目名称",
        column: ["yskmmc"],
    },
    {
        alias: "项目号",
        column: ["xmdh"],
    },
    {
        alias: "研发项目编码",
        column: ["xmid"],
    },
    {
        alias: "研发项目名称",
        column: ["xmmc"],
    },
    
    {
        alias: "出差申请单号",
        width: "200px",
        column: ["ccsqd"],
    },{
        alias: "申请单类型",
        width: "200px",
        column: ["sqdlxmc"],
    },
    {
        alias: "差标原则",
        column: ["sfwbmc"],
    },
    {
        alias: "违背差标事项",
        column: ["wbsxsm"],
    },
    {
        alias: "违背差标原因",
        column: ["wbyysm"],
    },
    {
        alias: "汇总单状态",
        column: ["hzd_tszt"],
    },
    {
        alias: "汇总单生成时间",
        width: "200px",
        column: ["hzd_bzrq"],
    },
    {
        alias: "汇总单生成人",
        column: ["hzd_sqrxm"],
    },
    {
        alias: "付款完成时间",
        column: ["hzd_tssj"],
    },{
        alias: "会展单号",
        column: ["mice_code"],
    },
    {
        alias: "里程",
        column: ["lc"],
    },
    {
        alias: "数据来源",
        column: ["sjly"],
    },
];
//国际机票
export const externalColumns = [

    {
        alias: "订单类型",
        width: "150px",
        column: ["ddlxmc"],
    },
    {
        alias: "是否退票",
        column: ["sftp_state"],
    },
    {
        alias: "机票票号",
        width: "150px",
        column: ["tkno"],
    },
    {
        alias: "原票号",
        width: "150px",
        column: ["ytkno"],
    },
    {
        alias: "结算单号",
        column: ["jsdh"],
    },{
        alias: "报账单号",
        width: "200px",
        column: ["hzd_id"],
    },
    {
        alias: "商互通单号",
        column: ["shtdh"],
    }, {
        alias: "实收/实退",
        column: ["fyje"],
    },  {
        alias: "业务发生时间",
        width: "200px",
        column: ["ywfssj"],
    },
    {
        alias: "是否会展订单",
        column: ["mice_flag_mc"],
    }, {
        alias: "企业简称",
        width: "200px",
        column: ["qyjc"],
    }, {
        alias: "公布运价",
        column: ["bzzdj"],
    },
    {
        alias: "供应商",
        column: ["supplier_name"],
    }, {
        alias: "政策节省",
        column: ["zcjs"],
    },  {
        alias: "法人公司编号",
        column: ["account_company_code"],
    },
    {
        alias: "法人公司",
        width: "250px",
        column: ["account_company_name"],
    },
    {
        alias: "领域名称",
        width: "250px",
        column: ["field_name"],
    },
    {
        alias: "平台",
        width: "250px",
        column: ["pt_name"],
    },
    {
        alias: "产业线",
        width: "250px",
        column: ["pl_name"],
    },

    {
        alias: "团队申请单号",
        width: "200px",
        column: ["tdsqdh"],
    },
    {
        alias: "订单编号",
        width: "200px",
        column: ["ddbh"],
    },
    {
        alias: "原订单编号",
        width: "200px",
        column: ["yddbh"],
    },
    {
        alias: "供应订单编号",
        width: "200px",
        column: ["cgptdh"],
    },
    {
        alias: "创建人工号",
        column: ["sqrgh_filter"],
    },
    {
        alias: "创建人",
        column: ["sqr_filter"],
    },
    {
        alias: "客票状态",
        column: ["jpzt"],
    },
    {
        alias: "机票状态",
        column: ["jpztmc"],
    },
    {
        alias: "行程单号",
        column: ["xcdh"],
    },
    {
        alias: "预订时间",
        width: "200px",
        column: ["ydsj"],
    },
    {
        alias: "改期时间",
        width: "200px",
        column: ["gqsj"],
    },
    {
        alias: "退票时间",
        width: "200px",
        column: ["tpsj"],
    },
    {
        alias: "出票时间",
        width: "200px",
        column: ["cpsj"],
    },
    {
        alias: "订票时间",
        width: "200px",
        column: ["dd_ydsj"],
    },
    {
        alias: "是否改签",
        column: ["sfgq_state"],
    },
    {
        alias: "乘机人",
        column: ["cxr_xm_filter"],
    }, {
        alias: "乘机人工号",
        column: ["cxr_gh_filter"],
    }, {
        alias: "乘机人部门",
        column: ["cxr_bmmc"],
    }, {
        alias: "航程类型",
        column: ["hclx"],
    }, {
        alias: "航程",
        column: ["hc"],
    }, {
        alias: "出发机场",
        column: ["cfzmc"],
    }, {
        alias: "出发机场三字码",
        column: ["cfz"],
    }, {
        alias: "到达机场",
        column: ["ddzmc"],
    }, {
        alias: "到达机场三字码",
        column: ["ddz"],
    }, {
        alias: "提前预订天数",
        column: ["tqydts"],
    },
    // {
    //     "alias": "证件号",
    //     "column": [
    //         "cxr_zjhm"
    //     ]
    // },
    {
        alias: "出发城市",
        column: ["pnr_cfcs_mc_filter"],
    },
    {
        alias: "到达城市",
        column: ["pnr_ddcs_mc_filter"],
    },
    {
        alias: "航空公司",
        column: ["hkgsmc"],
    }, {
        alias: "航班号",
        column: ["pnr_hbh_filter"],
    },
    {
        alias: "折扣",
        column: ["zkl"],
    },{
        alias: "前后N小时最低价",
        column: ["qhjxszdj"],
    },{
        alias: "差旅标准价",
        column: ["clbzj"],
    },{
        alias: "应收金额含服务费",
        column: ["ysjehfwf"],
    },{
        alias: "应结金额含服务费",
        column: ["yjjehfwf"],
    },
    {
        alias: "舱位码",
        column: ["jp_cw"],
    },{
        alias: "舱位类型",
        column: ["cwlx"],
    },
    {
        alias: "出发时间",
        width: "200px",
        column: ["jp_cfsj_filter"],
    },
    {
        alias: "到达时间",
        width: "200px",
        column: ["jp_ddsj_filter"],
    },
    {
        alias: "销售价",
        column: ["jg_xsj"],
    },
    {
        alias: "机建费",
        column: ["jg_jsf"],
    },
    {
        alias: "税费",
        column: ["jg_tax"],
    },
    {
        alias: "升舱费",
        column: ["scf"],
    },
    {
        alias: "改签手续费",
        column: ["gqsxf"],
    },
    {
        alias: "退票手续费费",
        column: ["tp_tpsxf"],
    },
    {
        alias: "超标金额",
        column: ["cbje"],
    },
    {
        alias: "TMC服务费",
        column: ["ptfwf"],
    },
    // {
    //     alias: "TMC服务费",
    //     column: ["fwffws"],
    // },
    {
        alias: "改签后票号",
        column: ["gqhph"],
    },
    {
        alias: "改签费用",
        column: ["gqfy"],
    },
    {
        alias: "退改签标志",
        column: ["sftgmc"],
    },
    {
        alias: "保险单号",
        column: ["zddbh"],
    },

    {
        alias: "保险保单号",
        column: ["bxbdh"],
    },

    {
        alias: "保险状态",
        column: ["bxztmc"],
    },
    {
        alias: "保险金额",
        column: ["bxje"],
    },
    {
        alias: "支付类型",
        column: ["pay_type"],
    },
    {
        alias: "支付科目",
        column: ["zf_zfkmmc"],
    },
    {
        alias: "交易流水号",
        width: "200px",
        column: ["zf_zflsh"],
    },
    {
        alias: "因公因私",
        column: ["clyymc"],
    },
    {
        alias: "国内国际",
        column: ["gngjmc"],
    },
    {
        alias: "供应商编号",
        column: ["gys"],
    },
    {
        alias: "预算人工号",
        column: ["budget_people_emp_num"],
    },
    {
        alias: "预算人姓名",
        column: ["budget_people_name"],
    },
    {
        alias: "预算类型",
        column: ["budget_source"],
    },
    {
        alias: "预算单号",
        width: "200px",
        column: ["ysdh"],
    },
    {
        alias: "预算部门编码",
        width: "200px",
        column: ["budget_department_code"],
    },
    {
        alias: "预算部门名称",
        width: "200px",
        column: ["budget_department_name"],
    },
    {
        alias: "费用项目编码",
        column: ["yskm"],
    },
    {
        alias: "费用项目名称",
        column: ["yskmmc"],
    },
    {
        alias: "项目号",
        column: ["xmdh"],
    },
    {
        alias: "研发项目编码",
        column: ["xmid"],
    },
    {
        alias: "研发项目名称",
        column: ["xmmc"],
    },
    
    {
        alias: "出差申请单号",
        width: "200px",
        column: ["ccsqd"],
    },{
        alias: "申请单类型",
        width: "200px",
        column: ["sqdlxmc"],
    },
    {
        alias: "差标原则",
        column: ["sfwbmc"],
    },
    {
        alias: "违背差标事项",
        column: ["wbsxsm"],
    },
    {
        alias: "违背差标原因",
        column: ["wbyysm"],
    },
    {
        alias: "汇总单状态",
        column: ["hzd_tszt"],
    },
    {
        alias: "汇总单生成时间",
        width: "200px",
        column: ["hzd_bzrq"],
    },
    {
        alias: "汇总单生成人",
        column: ["hzd_sqrxm"],
    },
    {
        alias: "付款完成时间",
        column: ["hzd_tssj"],
    },{
        alias: "会展单号",
        column: ["mice_code"],
    },
    {
        alias: "里程",
        column: ["lc"],
    },
    {
        alias: "数据来源",
        column: ["sjly"],
    }
  ];
//差旅酒店
export const travelHotelColumns = [
    {
        alias: "团队申请单号",
        width: "200px",
        column: ["tdsqdh"],
    },

    {
        alias: "订单编号",
        width: "200px",
        column: ["ddbh"],
    },
    {
        alias: "订单类型",
        width: "150px",
        column: ["ddlxmc"],
    },
    {
        alias: "原订单号",
        width: "200px",
        column: ["yddbh"],
    },
    {
        alias: "供应商订单号",
        width: "200px",
        column: ["gys_ddbh"],
    },
    {
        alias: "原始供应商订单号",
        width: "200px",
        column: ["ysgysddbh"],
    },
    {
        alias: "商互通单号",
        column: ["shtdh"],
    },
    {
        alias: "入住城市",
        column: ["rzcs_filter"],
    },
    {
        alias: "酒店名称",
        width: "200px",
        column: ["jdmc_filter"],
    },
    {
        alias: "酒店星级",
        column: ["jdxjmc_filter"],
    },
    {
        alias: "酒店地址",
        column: ["jddz_filter"],
    },
    {
        alias: "客房类型",
        column: ["kflx_filter"],
    },
    {
        alias: "房间顺序号",
        column: ["sjfjxh"],
    },
    {
        alias: "是否含早餐",
        column: ["sfhzc"],
    },
    {
        alias: "是否同住",
        column: ["is_share_with"],
    },
    {
        alias: "供应商",
        column: ["supplier_name"],
    },
    {
        alias: "预订时间",
        width: "200px",
        column: ["ydsj_filter"],
    },
    {
        alias: "业务发生时间",
        column: ["ywfssj"],
    },
    {
        alias: "预订入住日期",
        column: ["ydrzrq"],
    },
    {
        alias: "预订离店日期",
        column: ["ydldrq"],
    },
    {
        alias: "实际入住日期    ",
        column: ["sjrzrq_filter"],
    },
    {
        alias: "实际离店日期    ",
        column: ["sjldrq_filter"],
    },
    {
        alias: "实际房费",
        column: ["sjff"],
    },
    {
        alias: "退单日期",
        column: ["tdrq"],
    },
    {
        alias: "分摊间夜数",
        column: ["jys"],
    },
    {
        alias: "房间数",
        column: ["fjsl"],
    },
    {
        alias: "单价",
        column: ["jg_dj"],
    },
    {
        alias: "订单总价",
        column: ["fyje"],
    },
    {
        alias: "差旅标准价",
        column: ["clbzj"],
    },
    {
        alias: "超标金额",
        column: ["cbje"],
    },
    {
        alias: "实际费用金额",
        column: ["sjfyje"],
    },
    {
        alias: "实际差旅标准价",
        column: ["sjclbzj"],
    },
    {
        alias: "政策节省",
        column: ["zcjs"],
    },
    {
        alias: "TMC服务费",
        column: ["ptfwf"],
    },
    // {
    //     alias: "TMC服务费",
    //     column: ["fwffws"],
    // },
    {
        alias: "订单状态",
        column: ["ddztmc"],
    },
    {
        alias: "创建人工号",
        column: ["sqrgh"],
    },
    {
        alias: "创建人",
        column: ["sqr"],
    },
    {
        alias: "创建时间",
        width: "200px",
        column: ["dd_ydsj"],
    },
    {
        alias: "入住人",
        column: ["rzr_filter"],
    },
    {
        alias: "入住人部门名称",
        column: ["rzr_bmmc"],
    },
    {
        alias: "支付类型",
        column: ["pay_type"],
    },
    {
        alias: "支付科目",
        column: ["zf_zfkmmc"],
    },
    {
        alias: "预算人工号",
        column: ["budget_people_emp_num"],
    },
    {
        alias: "预算人姓名",
        column: ["budget_people_name"],
    },
    {
        alias: "预算类型",
        column: ["budget_source"],
    },
    {
        alias: "退改签标志",
        column: ["sftgmc"],
    },
    {
        alias: "预算单号",
        width: "200px",
        column: ["ysdh"],
    },
    {
        alias: "预算部门编码",
        width: "200px",
        column: ["budget_department_code"],
    },
    {
        alias: "预算部门名称",
        width: "200px",
        column: ["budget_department_name"],
    },
    {
        alias: "企业简称",
        width: "200px",
        column: ["qyjc"],
    },
    {
        alias: "费用项目编码",
        column: ["yskm"],
    },
    {
        alias: "费用项目名称",
        column: ["yskmmc"],
    },
    {
        alias: "项目号",
        column: ["xmdh"],
    },
    {
        alias: "研发项目编码",
        column: ["xmid"],
    },
    {
        alias: "研发项目名称",
        column: ["xmmc"],
    },
    {
        alias: "法人公司编号",
        column: ["account_company_code"],
    },
    {
        alias: "法人公司",
        width: "250px",
        column: ["account_company_name"],
    },
    {
        alias: "领域名称",
        width: "250px",
        column: ["field_name"],
    },
    {
        alias: "平台",
        width: "250px",
        column: ["pt_name"],
    },
    {
        alias: "产业线",
        width: "250px",
        column: ["pl_name"],
    },
    {
        alias: "结算单号",
        column: ["jsdh"],
    },
    {
        alias: "出差申请单号",
        width: "200px",
        column: ["ccsqd"],
    },

    {
        alias: "因公因私",
        width: "200px",
        column: ["clyymc"],
    },

    {
        alias: "入住人工号",
        column: ["rzr_gh"],
    },
    {
        alias: "差标原则",
        column: ["cl_sfwb_mc"],
    },
    {
        alias: "差标违背原因",
        column: ["cl_wbyy"],
    },
    {
        alias: "汇总单状态",
        column: ["hzd_tszt"],
    },
    {
        alias: "报账单号",
        width: "200px",
        column: ["hzd_id"],
    },
    {
        alias: "汇总单生成时间",
        width: "200px",
        column: ["hzd_bzrq"],
    },
    {
        alias: "汇总单生成人",
        column: ["hzd_sqrxm"],
    },
    {
        alias: "付款完成时间",
        column: ["hzd_tssj"],
    }, {
        alias: "数据来源",
        column: ["sjly"],
    },
  ];
//差旅用车
export const travelTaxiColumns = [
    {
        alias: "订单编号",
        width: "200px",
        column: ["ddbh"],
    },
    {
        alias: "订单类型",
        width: "150px",
        column: ["ddlxmc"],
    },
    {
        alias: "原订单号",
        width: "200px",
        column: ["yddbh"],
    },
    {
        alias: "供应商订单号",
        width: "200px",
        column: ["gys_ddbh"],
    },
    {
        alias: "商互通单号",
        column: ["shtdh"],
    },
    {
        alias: "创建人工号",
        column: ["sqrgh"],
    },
    {
        alias: "创建人",
        column: ["sqr"],
    },
    {
        alias: "创建时间",
        width: "200px",
        column: ["ydsj"],
    },
    {
        alias: "预订时间",
        width: "200px",
        column: ["ydsj"],
    },
    {
        alias: "业务发生时间",
        width: "200px",
        column: ["ywfssj"],
    },
    {
        alias: "支付完成时间",
        width: "200px",
        column: ["sk_sj"],
    },
    {
        alias: "计划用车时间",
        column: ["jhycsj"],
    },
    {
        alias: "实际用车时间",
        column: ["sjscsj"],
    },
    {
        alias: "订单状态",
        column: ["ddztmc"],
    },
    {
        alias: "城市",
        column: ["cfcsmc"],
    },
    {
        alias: "用车里程（公里）",
        column: ["bdlc"],
    },
    {
        alias: "计划上车地点",
        column: ["cfxxdz_filter"],
    },
    {
        alias: "计划下车地点",
        column: ["ddxxdz_filter"],
    },
    {
        alias: "实际上车地点",
        column: ["sjscdz_filter"],
    },
    {
        alias: "实际下车地点",
        column: ["sjxcdz_filter"],
    },
    {
        alias: "供应商",
        column: ["gys_mc"],
    },
    {
        alias: "车型名称",
        column: ["cxmc"],
    },
    {
        alias: "车型类别",
        column: ["cxzmc"],
    },
    {
        alias: "车牌号",
        column: ["cph"],
    },
    {
        alias: "乘车人工号",
        column: ["cxr_gh_filter"],
    },
    {
        alias: "乘车人姓名",
        column: ["cxr_xm_filter"],
    },
    {
        alias: "乘车人部门名称",
        column: ["cxr_bmmc"],
    },
    {
        alias: "订单金额",
        column: ["jg_ysje"],
    },
    {
        alias: "超标金额",
        column: ["cbje"],
    },
    {
        alias: "企业支付金额",
        column: ["qkje"],
    },
    {
        alias: "支付状态",
        column: ["sk_ztmc"],
    },
    {
        alias: "支付科目",
        column: ["zf_zfkmmc"],
    },
    {
        alias: "退款金额",
        column: ["jg_ytje"],
    },
    {
        alias: "退款状态",
        column: ["tk_ztmc"],
    },
    {
        alias: "退款时间",
        column: ["tk_sj"],
    },
    {
        alias: "服务费",
        column: ["fws_ptfwf"],
    },
    {
        alias: "预算人工号",
        column: ["budget_people_emp_num"],
    },
    {
        alias: "预算人姓名",
        column: ["budget_people_name"],
    },
    {
        alias: "预算类型",
        column: ["budget_source"],
    },
    {
        alias: "预算单号",
        width: "200px",
        column: ["ysdh"],
    },
    {
        alias: "预算部门编码",
        width: "200px",
        column: ["budget_department_code"],
    },
    {
        alias: "预算部门名称",
        width: "200px",
        column: ["budget_department_name"],
    },
    {
        alias: "企业简称",
        width: "200px",
        column: ["qyjc"],
    },
    {
        alias: "费用项目编码",
        column: ["yskm"],
    },
    {
        alias: "费用项目名称",
        column: ["yskmmc"],
    },
    {
        alias: "项目号",
        column: ["xmdh"],
    },
    {
        alias: "研发项目编码",
        column: ["xmid"],
    },
    {
        alias: "研发项目名称",
        column: ["xmmc"],
    },
    {
        alias: "结算单位编码",
        column: ["account_company_code"],
    },
    {
        alias: "结算单位名称",
        width: "250px",
        column: ["account_company_name"],
    },
    {
        alias: "领域名称",
        width: "250px",
        column: ["field_name"],
    },
    {
        alias: "平台",
        width: "250px",
        column: ["pt_name"],
    },
    {
        alias: "产业线",
        width: "250px",
        column: ["pl_name"],
    },
    {
        alias: "结算单号",
        column: ["jsdh"],
    },
    {
        alias: "出差申请单号",
        width: "200px",
        column: ["ccsqd"],
    },
    {
        alias: "汇总单状态",
        column: ["hzd_tszt"],
    },
    {
        alias: "报账单号",
        width: "200px",
        column: ["hzd_id"],
    },
    {
        alias: "汇总单生成时间",
        width: "200px",
        column: ["hzd_bzrq"],
    },
    {
        alias: "汇总单生成人",
        column: ["hzd_sqrxm"],
    },
    {
        alias: "付款完成时间",
        column: ["hzd_tssj"],
    }, {
        alias: "数据来源",
        column: ["sjly"],
    }
];

// 团队票
export const teamColumns = [
    {
      alias: "团队票单号",
      width: "200px",
      column: ["destine_no"],
    },

    {
      alias: "出发地",
      width: "200px",
      column: ["begin_city_name"],
    },

    {
      alias: "目的地",
      width: "200px",
      column: ["end_city_name"],
    },

    {
      alias: "出差类型",
      width: "200px",
      column: ["evection_type_desc"],
    },

    {
      alias: "出发日期",
      width: "200px",
      column: ["begin_date"],
    },

    {
      alias: "返回日期",
      width: "200px",
      column: ["end_date"],
    },


    {
      alias: "产品类型",
      width: "200px",
      column: ["info_desc"],
    },

    {
      alias: "订单负责人工号",
      width: "200px",
      column: ["transactor_code"],
    },

    {
      alias: "订单负责人名称",
      width: "200px",
      column: ["transactor_name"],
    },

    {
      alias: "接收时间",
      width: "200px",
      column: ["receive_time"],
    },

    {
      alias: "联系人工号",
      width: "200px",
      column: ["contact_user_code"],
    },
    {
      alias: "联系人名称",
      width: "200px",
      column: ["contact_user_name"],
    },
    {
      alias: "联系人电话",
      width: "200px",
      column: ["contact_user_phone"],
    },
    {
      alias: "联系人邮箱",
      width: "200px",
      column: ["contact_user_mail"],
    },
    {
      alias: "联系人所属部门",
      width: "200px",
      column: ["contact_dept_name"],
    },
    {
      alias: "订单状态",
      width: "200px",
      column: ["status_desc"],
    },
    {
      alias: "汇总金额",
      width: "200px",
      column: ["total_amount"],
    }, {
      alias: "非团队金额",
      width: "200px",
      column: ["non_team_amount"],
    },
    {
      alias: "节省金额",
      width: "200px",
      column: ["save_amount"],
    },
    {
      alias: "国际机票退款金额",
      width: "200px",
      column: ["international_airfare_refund_amount "],
    },
    {
      alias: "国内机票退款金额",
      width: "200px",
      column: ["domestic_airfare_refund_amount"],
    },
    {
      alias: "酒店退款金额",
      width: "200px",
      column: ["hotel_refund_amount"],
    },
    {
      alias: "是否出票",
      width: "200px",
      column: ["flag_desc"],
    },
    {
      alias: "创建人工号",
      width: "200px",
      column: ["create_by"],
    },
    {
      alias: "创建人",
      width: "200px",
      column: ["create_name"],
    },
    {
      alias: "最后一次更新人工号",
      width: "200px",
      column: ["last_modified_by"],
    },
    {
      alias: "最后一次更新人",
      width: "200px",
      column: ["last_modified_name"],
    },
    {
      alias: "创建时间",
      width: "200px",
      column: ["gmt_create"],
    },
    {
      alias: "更新时间",
      width: "200px",
      column: ["gmt_modified"],
    },

  ]

//差旅地面服务
export const travelgroupColumns = [
    {
        alias: "订单编号",
        width: "200px",
        column: ["ddbh"],
    },
    {
        alias: "订单类型",
        width: "150px",
        column: ["ddlxmc"],
    },
    {
        alias: "原订单号",
        width: "200px",
        column: ["yddbh"],
    },
    {
        alias: "供应商订单号",
        width: "200px",
        column: ["gys_ddbh"],
    },
    {
        alias: "商互通单号",
        column: ["shtdh"],
    },
    {
        alias: "创建人工号",
        column: ["sqrgh"],
    },
    {
        alias: "创建人",
        column: ["sqr"],
    },
    {
        alias: "创建时间",
        width: "200px",
        column: ["ydsj"],
    },
    {
        alias: "预订时间",
        width: "200px",
        column: ["ydsj"],
    },
    {
        alias: "业务发生时间",
        width: "200px",
        column: ["ywfssj"],
    },
    {
        alias: "支付完成时间",
        width: "200px",
        column: ["zf_datetime"],
    },
    {
        alias: "使用人工号",
        column: ["cxr_gh_filter"],
    },
    {
        alias: "使用人姓名",
        column: ["cxr_xm_filter"],
    }, {
        alias: "使用人部门名称",
        column: ["cxr_bmmc"],
    },
    {
        alias: "出行时间",
        column: ["cxsj"],
    },
    {
        alias: "产品名称",
        column: ["cpmc"],
    },
    {
        alias: "服务站点",
        column: ["zdmc_filter"],
    },
    {
        alias: "服务站点地址",
        column: ["fwdz_filter"],
    },
    {
        alias: "销售价",
        column: ["xsj"],
    },
    {
        alias: "实收/实退",
        column: ["fyje"],
    },
    {
        alias: "支付状态",
        column: ["zfztmc"],
    },
    {
        alias: "差旅类型",
        column: ["clyymc"],
    },
    {
        alias: "支付类型",
        column: ["pay_type"],
    },
    {
        alias: "支付科目",
        column: ["zf_zfkmmc"],
    },
    {
        alias: "预算人工号",
        column: ["budget_people_emp_num_filter"],
    },
    {
        alias: "预算人姓名",
        column: ["budget_people_name_filter"],
    },
    {
        alias: "预算类型",
        column: ["budget_source"],
    },
    {
        alias: "预算单号",
        width: "200px",
        column: ["budget_source"],
    },
    {
        alias: "预算部门编码",
        width: "200px",
        column: ["budget_department_code"],
    },
    {
        alias: "预算部门名称",
        width: "200px",
        column: ["budget_department_name"],
    },
    {
        alias: "企业简称",
        width: "200px",
        column: ["qyjc"],
    },
    {
        alias: "费用项目编码",
        column: ["yskm"],
    },
    {
        alias: "费用项目名称",
        column: ["yskmmc"],
    },
    {
        alias: "项目号",
        column: ["xmid"],
    },
    {
        alias: "研发项目编码",
        column: ["xmdh"],
    },
    {
        alias: "研发项目名称",
        column: ["xmmc"],
    },
    {
        alias: "结算单位编码",
        column: ["account_company_code"],
    },
    {
        alias: "结算单位名称",
        width: "250px",
        column: ["account_company_name"],
    },
    {
        alias: "领域名称",
        width: "250px",
        column: ["field_name"],
    },
    {
        alias: "平台",
        width: "250px",
        column: ["pt_name"],
    },
    {
        alias: "产业线",
        width: "250px",
        column: ["pl_name"],
    },
    {
        alias: "出差申请单号",
        width: "200px",
        column: ["ccsqd"],
    },
    {
        alias: "结算单号",
        width: "200px",
        column: ["jsdh"],
    },
    {
        alias: "汇总单状态",
        column: ["hzd_tszt"],
    },
    {
        alias: "报账单号",
        width: "200px",
        column: ["hzd_id"],
    },
    {
        alias: "汇总单生成时间",
        width: "200px",
        column: ["hzd_bzrq"],
    },
    {
        alias: "汇总单生成人",
        column: ["hzd_sqrxm"],
    },
    {
        alias: "付款完成时间",
        column: ["hzd_tssj"],
    }, {
        alias: "数据来源",
        column: ["sjly"],
    }
];
//差旅申请状态报表
export const travelApplicationStatusColumns = [
    {
        alias: "出差申请单号",
        width: "200px",
        column: ["djbh"],
    },
    {
        alias: "创建人工号",
        column: ["sqrgh_filter"],
    },
    {
        alias: "创建人",
        column: ["sqrxm_filter"],
    },
    {
        alias: "创建时间",
        width: "150px",
        column: ["sqsj_date_key"],
    },
    {
        alias: "审批通过时间",
        width: "150px",
        column: ["spwcsj"],
    },
    {
        alias: "行程结束确认时间",
        width: "150px",
        column: ["sjccrq"],
    },
    {
        alias: "经办人所属部门",
        column: ["sqbmmc"],
    },
    {
        alias: "出差人工号",
        column: ["yggh_filter"],
    },
    {
        alias: "出差人",
        column: ["ygxm_filter"],
    },
    {
        alias: "出差人所属部门",
        column: ["bmmc"],
    },
    {
        alias: "出行人",
        column: ["ccry_filter"],
    },
    {
        alias: "预算人工号",
        column: ["budget_people_emp_num"],
    },
    {
        alias: "预算人",
        column: ["budget_people_name"],
    },
    {
        alias: "预算部门编码",
        width: "200px",
        column: ["budget_department_code"],
    },
    {
        alias: "预算部门名称",
        width: "200px",
        column: ["budget_department_name"],
    },
    {
        alias: "预算类型",
        column: ["budget_source"],
    },
    {
        alias: "成本中心",
        column: ["cbzxmc"],
    },
    {
        alias: "卡奥斯项目",
        column: ["kaaosi"],
    },
    {
        alias: "结算单位编码",
        column: ["account_company_code"],
    },
    {
        alias: "结算单位名称",
        width: "250px",
        column: ["account_company_name"],
    },
    {
        alias: "领域名称",
        width: "250px",
        column: ["field_name"],
    },
    {
        alias: "平台",
        width: "250px",
        column: ["pt_name"],
    },
    {
        alias: "产业线",
        width: "250px",
        column: ["pl_name"],
    },
    {
        alias: "出发城市",
        column: ["cfdmc"],
    },
    {
        alias: "到达城市",
        column: ["mddmc"],
    },
    {
        alias: "计划出差日期始",
        column: ["ccrqs"],
    },
    {
        alias: "计划出差日期止",
        column: ["ccrqz"],
    },
    {
        alias: "实际出差日期始",
        column: ["sjccrqs"],
    },
    {
        alias: "实际出差日期止",
        column: ["sjccrqz"],
    },
    {
        alias: "审批状态",
        column: ["spzt"],
    },
    {
        alias: "单据来源",
        column: ["ddly"],
    },
    {
        alias: "事前事后",
        column: ["single_point"],
    }, {
        alias: "出差目标",
        column: ["ccsy"],
    },
    {
        alias: "单据状态",
        column: ["ddzt"],
    },
    {
        alias: "行程确认方式",
        column: ["travel_confirm_method"],
    },
    {
        alias: "预算金额",
        column: ["fyys"],
    },
    {
        alias: "已使用总金额",
        column: ["ysyzje"],
    },
    {
        alias: "剩余可用预算金额",
        column: ["syysje"],
    },
    // {
    //     alias: "机票预算",
    //     column: ["fyys_fj"],
    // },
    {
        alias: "交通预算",
        column: ["jtfy"],
    },
    {
        alias: "酒店预算",
        column: ["zsfy"],
    },
    {
        alias: "用车预算",
        column: ["ycfy"],
    },
    {
        alias: "地面服务预算",
        column: ["travel_service_fee"],
    },
    {
        alias: "保险预算",
        column: ["insurance_fee"],
    },
    {
        alias: "是否超标",
        column: ["sfcb"],
    },
    {
        alias: "超标原因",
        column: ["cbyymc"],
    }, {
        alias: "数据来源",
        column: ["sjly"],
    }
];

// 异地宴请申请单
export const banquetApplicationColumns = [{
    alias: "申请单号",
    width: "200px",
    column: ["order_code"],
    fixed: "left"
}, {
    alias: "申请类型",
    width: "200px",
    column: ["scene_type_name"]
}, {
    alias: "经办人工号",
    width: "200px",
    column: ["creator"]
}, {
    alias: "经办人姓名",
    width: "200px",
    column: ["creator_name"]
}, {
    alias: "预算人工号",
    width: "200px",
    column: ["budgeter_code"]
}, {
    alias: "预算人姓名",
    width: "200px",
    column: ["budgeter_name"]
}, {
    alias: "申请开始时间",
    width: "200px",
    column: ["estimated_meal_time_start"]
}, {
    alias: "申请结束时间",
    width: "200px",
    column: ["estimated_meal_time_end"]
}, {
    alias: "申请事由",
    width: "200px",
    column: ["banquet_reason"]
}, {
    alias: "申请金额",
    width: "200px",
    column: ["budget_amount"]
}, {
    alias: "实际使用金额",
    width: "200px",
    column: ["actual_payment_amount"]
}, {
    alias: "预算剩余金额",
    width: "200px",
    column: ["budget_left"]
}, {
    alias: "预算释放金额",
    width: "200px",
    column: ["budget_release"]
}, {
    alias: "用餐城市-省",
    width: "200px",
    column: ["meal_location_province"]
}, {
    alias: "用餐城市-市",
    width: "200px",
    column: ["meal_location_city"]
}, {
    alias: "餐厅名称",
    width: "200px",
    column: ["restaurant_name"]
}, {
    alias: "预算来源",
    width: "200px",
    column: ["budget_source_code"]
}, {
    alias: "支付单号",
    width: "200px",
    column: ["pay_code"]
}, {
    alias: "费用科目",
    width: "200px",
    column: ["fee_item_name"]
}, {
    alias: "预算部门",
    width: "200px",
    column: ["budget_department_name"]
}, {
    alias: "订单状态",
    width: "200px",
    column: ["order_status_name"]
}, {
    alias: "审批状态",
    width: "200px",
    column: ["approve_status_name"]
}, {
    alias: "使用状态",
    width: "200px",
    column: ["use_status"]
}];

// 异地宴请预订单
export const banquetBookingColumns = [{
    alias: "预订单号",
    width: "200px",
    column: ["order_booking_code"],
    fixed: "left"
}, {
    alias: "美团预订单号",
    width: "200px",
    column: ["mt_booking_code"]
}, {
    alias: "申请单号",
    width: "200px",
    column: ["order_code"]
}, {
    alias: "申请类型",
    width: "200px",
    column: ["scene_type_name"]
}, {
    alias: "签单人工号",
    width: "200px",
    column: ["signer_code"]
}, {
    alias: "签单人姓名",
    width: "200px",
    column: ["signer_name"]
}, {
    alias: "签到时间",
    width: "200px",
    column: ["checkin_time"]
}, {
    alias: "用餐城市-省",
    width: "200px",
    column: ["meal_location_province_booking"]
}, {
    alias: "用餐城市-市",
    width: "200px",
    column: ["meal_location_city_booking"]
}, {
    alias: "餐厅名称",
    width: "200px",
    column: ["restaurant_name_booking"]
}, {
    alias: "支付方式",
    width: "200px",
    column: ["second_business_name"]
}, {
    alias: "支付类型",
    width: "200px",
    column: ["pay_type_name"]
}, {
    alias: "实际支付金额（企业）",
    width: "200px",
    column: ["ent_pay_amount"]
}, {
    alias: "服务费",
    width: "200px",
    column: ["realtime_service_fee"]
}, {
    alias: "个人支付金额",
    width: "200px",
    column: ["staff_pay_amount"]
}, {
    alias: "订单实付金额（总计）",
    width: "200px",
    column: ["actual_payment_amount_booking"]
}, {
    alias: "订单原价",
    width: "200px",
    column: ["order_origin_price"]
}, {
    alias: "节约金额",
    width: "200px",
    column: ["total_reduce_amount"]
// }, {
//     alias: "最终支付金额",
//     width: "200px",
//     column: ["order_final_pay"]
// }, {
//     alias: "企业最终支付金额",
//     width: "200px",
//     column: ["ent_final_pay"]
}, {
    alias: "最终支付金额",
    width: "200px",
    column: ["ent_final_pay"]
}, {
    alias: "退款金额",
    width: "200px",
    column: ["total_refund_amount"]
}, {
    alias: "支付单号",
    width: "200px",
    column: ["pay_code"]
}, {
    alias: "预算来源",
    width: "200px",
    column: ["budget_source_code"]
}, {
    alias: "费用科目",
    width: "200px",
    column: ["fee_item_name"]
}, {
    alias: "预算部门",
    width: "200px",
    column: ["budget_department_name"]
}, {
    alias: "结算单位",
    width: "200px",
    column: ["account_company_name"]
}, {
    alias: "预定单同步时间",
    width: "200px",
    column: ["create_time_booking"]
}, {
    alias: "订单状态",
    width: "200px",
    column: ["order_status_booking_show"]
}, {
    alias: "汇总状态",
    width: "200px",
    column: ["statement_status_name"]
}, {
    alias: "对帐单号",
    width: "200px",
    column: ["settle_code"]
}, {
    alias: "中台汇总单号",
    width: "200px",
    column: ["account_code"]
}, {
    alias: "商互通账单号",
    width: "200px",
    column: ["cvp_code"]
}, {
    alias: "支付完成时间",
    width: "200px",
    column: ["balance_payment_time"]
}]

// 创客帮
export const helperColumns = [{
    alias: "发布人工号",
    width: "1px",
    column: ["create_user"],
    fixed: "left"
}, {
    alias: "发布人",
    width: "1px",
    column: ["create_user_name"],
}, {
    alias: "发布时间",
    width: "2px",
    column: ["create_time"]
}, {
    alias: "出发城市",
    width: "2px",
    column: ["from_city_name"]
}, {
    alias: "送达城市",
    width: "2px",
    column: ["dest_city_name"]
}, {
    alias: "物品类型",
    width: "2px",
    column: ["object_type_name"]
}, {
    alias: "需求状态",
    width: "2px",
    column: ["piggyback_status_name"]
}, {
    alias: "需求接受人工号",
    width: "2px",
    column: ["accept_user_code"]
}, {
    alias: "需求接受人",
    width: "2px",
    column: ["accept_user_name"]
}, {
    alias: "接受时间",
    width: "2px",
    column: ["accept_update_time"]
}, {
    alias: "是否删除",
    width: "2px",
    column: ["is_delete_name"]
}];

//将datart字段转化为表格数据
export const aggregatorsToColumn = (aggregators: any) => {
    return aggregators?.map((item: any, index: number) => {
        return {
            title: item.alias,
            dataIndex: index,
            width: item.width ? item.width : "150px",
            ellipsis: true,
            customFilterDropdown: true,
            key:item.column[0]
        };
    });
};

//青岛订餐
export const travelorderFoodColumns = [
    {
        alias: "订单号",
        width: "200px",
        column: ["order_code"],
    },
    {
        alias: "订单状态",
        column: ["order_state"],
    },
    {
        alias: "订单创建时间",
        width: "200px",
        column: ["order_create_datetime"],
    },
    {
        alias: "酒店编号",
        column: ["order_hotel_id"],
    },
    {
        alias: "酒店名称",
        width: "200px",
        column: ["order_hotel_name"],
    },
    {
        alias: "经办人工号",
        column: ["order_owner_user_code_filter"],
    },
    {
        alias: "经办人姓名",
        column: ["order_owner_name_filter"],
    },
    {
        alias: "业务申请人",
        column: ["order_applicant_name_filter"],
    },
    {
        alias: "业务申请人部门",
        column: ["order_applicant_department"],
    },
    {
        alias: "有效签单人",
        column: ["bill_signer_name_filter"],
    },
    {
        alias: "陪同人数量",
        column: ["accompany_count"],
    },
    {
        alias: "来宾数量",
        column: ["guest_count"],
    },
    {
        alias: "来宾单位",
        column: ["order_guest_company"],
    },
    {
        alias: "工作餐提取人数",
        column: ["order_working_lunch_count"],
    },
    {
        alias: "支付方式",
        column: ["order_pay_type"],
    },
    {
        alias: "就餐时间",
        width: "200px",
        column: ["order_eating_datetime"],
    },
    {
        alias: "餐类",
        column: ["cate_type"],
    },
    {
        alias: "市场价格",
        column: ["order_public_amount"],
    },
    {
        alias: "政策节省",
        column: ["discount_amount"],
    },
    {
        alias: "合计金额",
        column: ["order_actual_amount"],
    },
    {
        alias: "直线审批人",
        column: ["direct_manager_name_filter"],
    },
    {
        alias: "直线审批状态",
        column: ["direct_manager_audit_state"],
    },
    {
        alias: "直线审批时间",
        width: "200px",
        column: ["direct_manager_audit_datetime"],
    },
    {
        alias: "二线审批人",
        column: ["indirect_manager_name"],
    },
    {
        alias: "二线审批状态",
        column: ["indirect_manager_audit_state"],
    },
    {
        alias: "二线审批时间",
        width: "200px",
        column: ["indirect_manager_audit_datetime"],
    },
    {
        alias: "预约管理员锁单状态",
        width: "200px",
        column: ["order_is_locked"],
    },
    {
        alias: "锁单时间",
        width: "200px",
        column: ["order_locked_datetime"],
    },
    {
        alias: "预算人工号",
        column: ["budgeter_id"],
    },
    {
        alias: "预算人姓名",
        column: ["budgeter_name"],
    },

    {
        alias: "预算类型",
        column: ["budget_source"],
    },
    {
        alias: "预算单号",
        width: "200px",
        column: ["budget_order_code"],
    },
    {
        alias: "预算部门编码",
        width: "200px",
        column: ["budget_department_code"],
    },
    {
        alias: "预算名称",
        column: ["budget_department_name"],
    },

    {
        alias: "领域名称",
        width: "250px",
        column: ["field_name"],
    },
    {
        alias: "平台",
        width: "250px",
        column: ["pt_name"],
    },
    {
        alias: "产业线",
        width: "250px",
        column: ["pl_name"],
    },

    {
        alias: "提交账单时间",
        column: ["bill_create_datetime"],
    },
    {
        alias: "结算金额",
        column: ["account_total_amount"],
    },
    {
        alias: "账单核对状态",
        column: ["bill_state"],
    },
    {
        alias: "账单核对时间",
        column: ["bill_check_datetime"],
    },
    {
        alias: "发票状态",
        column: ["account_invoice_state"],
    },
    {
        alias: "发票上传时间",
        column: ["account_invoice_create_datetime"],
    },
    {
        alias: "发票号",
        column: ["account_invoice_no"],
    },
    {
        alias: "供应商编码",
        column: ["order_provider_code"],
    },
    {
        alias: "结算单位编码",
        column: ["account_company_code"],
    },
    {
        alias: "结算单位名称",
        width: "250px",
        column: ["account_company_name"],
    },

    {
        alias: "汇总单状态",
        column: ["account_state"],
    },
    {
        alias: "汇总单号",
        width: "200px",
        column: ["account_code"],
    },
    {
        alias: "汇总单生成时间",
        width: "200px",
        column: ["account_create_datetime"],
    },
    {
        alias: "汇总单生成人",
        column: ["account_create_by"],
    },
    {
        alias: "付款完成时间",
        column: ["payment_success_datetime"],
    },
];

//数据中台业务类型
export const businessType = [
    {
        text: "青岛订房",
        key: "bookingHotel",
        columns:
            !checkUserGroups([UserGroupSystemConstant.SUPER_MANAGE.groupId, UserGroupSystemConstant.REPORT_CONTROL.groupId], 'OR')
                ? bookingColumns.splice(bookingColumns.length - 5, 5)
                : bookingColumns,
    },
    {
        text: "差旅-火车票",
        key: "travel-train",
        columns:
            !checkUserGroups([UserGroupSystemConstant.SUPER_MANAGE.groupId, UserGroupSystemConstant.REPORT_CONTROL.groupId], 'OR')
                ? trainColumns.splice(trainColumns.length - 6, 6)
                : trainColumns,
    },
    {
        text: "差旅-国内机票",
        key: "travel-internal",
        columns: !checkUserGroups([UserGroupSystemConstant.SUPER_MANAGE.groupId, UserGroupSystemConstant.REPORT_CONTROL.groupId], 'OR')
            ? internalColumns.splice(internalColumns.length - 6, 6)
            : internalColumns,
    },
    {
        text: "差旅-国际机票",
        key: "travel-external",
        columns:
            !checkUserGroups([UserGroupSystemConstant.SUPER_MANAGE.groupId, UserGroupSystemConstant.REPORT_CONTROL.groupId], 'OR')
                ? externalColumns.splice(externalColumns.length - 6, 6)
                : externalColumns,
    },
    {
        text: "差旅-酒店",
        key: "travel-hotel",
        columns:
            !checkUserGroups([UserGroupSystemConstant.SUPER_MANAGE.groupId, UserGroupSystemConstant.REPORT_CONTROL.groupId], 'OR')
                ? travelHotelColumns.splice(travelHotelColumns.length - 5, 5)
                : travelHotelColumns,
    },
    {
        text: "差旅-团队票",
        key: "travel-team",
        columns:
            !checkUserGroups([UserGroupSystemConstant.SUPER_MANAGE.groupId, UserGroupSystemConstant.REPORT_CONTROL.groupId], 'OR')
                ? teamColumns.splice(teamColumns.length - 5, 5)
                : teamColumns,
    },
    {
        text: "差旅-用车",
        key: "travel-taxi",
        columns: !checkUserGroups([UserGroupSystemConstant.SUPER_MANAGE.groupId, UserGroupSystemConstant.REPORT_CONTROL.groupId], 'OR')
            ? travelTaxiColumns.splice(travelTaxiColumns.length - 6, 6)
            : travelTaxiColumns,
    },
    {
        text: "差旅-出差申请",
        key: "travel-taxi",
        columns: travelApplicationStatusColumns,
    },
    {
        text: "差旅-地面服务",
        key: "travel-ground",
        columns:
            !checkUserGroups([UserGroupSystemConstant.SUPER_MANAGE.groupId, UserGroupSystemConstant.REPORT_CONTROL.groupId], 'OR')
                ? travelgroupColumns.splice(travelgroupColumns.length - 6, 6)
                : travelgroupColumns,
    },
    {
        text: "青岛订餐",
        key: "travel-orderFood",
        columns:
            !checkUserGroups([UserGroupSystemConstant.SUPER_MANAGE.groupId, UserGroupSystemConstant.REPORT_CONTROL.groupId], 'OR')
                ? travelorderFoodColumns.splice(
                    travelorderFoodColumns.length - 6,
                    6
                )
                : travelorderFoodColumns,
    },
];


export const budgetTypeOptions = [{
    name: 'BCC',
    code: 'BCC'
},
{
    name: 'GEMS',
    code: 'GEMS'
},
{
    name: 'KEMS',
    code: 'KEMS'
},
{
    name: 'RRSGEMS',
    code: 'RRSGEMS'
},
{
    name: 'MICRO',
    code: 'MICRO'
},
]