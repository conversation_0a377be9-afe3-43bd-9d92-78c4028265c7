<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Switch as hSwitch,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Image as hImage,
  message,
  Button as hButton,
} from 'ant-design-vue';
import {
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { computed, ref, watch, onMounted } from 'vue';
import { fileApi, workOrderApi } from '@haierbusiness-front/apis';
import type { Ref } from 'vue';
import { workOrderQuery } from '@haierbusiness-front/common-libs';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil';
import { HeaderConstant } from '@haierbusiness-front/common-libs';
import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
import callHistory from './callHistory.vue';
const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);

interface Props {
  show: boolean;
  id: number;
  callHistoryInfo: any;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  callHistoryInfo: null,
});

watch(
  () => props.callHistoryInfo,
  (newValue, oldValue) => {
    console.log(`message prop changed from ${oldValue} to ${newValue}`);
  },
);

const fileList = ref<any[]>([]);
const indexData = ref<any>({
  fileAddress: [],
  callId: '',
  workUsername: '',
  workNum: '',
  mobile: '',
  status: null,
});

const addfrom = ref();
const confirmLoading = ref(false);
const callHistoryShow = ref(false);
const rules: Record<string, Rule[]> = {
  detailsDesc: [{ required: true, message: '请输入明细描述' }],
  callId: [{ required: true, message: '请选择关联通话记录' }],
  status: [{ required: true, message: '请选择工单状态' }],
};

const emit = defineEmits(['cancel', 'ok']);

const visible = computed(() => props.show);

// 添加明细
const addDetail = () => {
  addfrom.value.validate().then(() => {
    fileList.value.forEach((item: any) => {
      if (
        window.location.origin.indexOf('http://127.0.0.1') !== -1 ||
        window.location.origin.indexOf('http://localhost') !== -1
      ) {
        if (item.response) {
          indexData.value.fileAddress.push({
            fileAddress: `https://travel-test.haier.net/${item.response.data.path}`,
            fileName: item.name,
          });
        } else {
          indexData.value.fileAddress.push({ fileAddress: item.url, fileName: item.name });
        }
      } else {
        if (item.response) {
          indexData.value.fileAddress.push({
            fileAddress: `${window.location.origin}/${item.response.data.path}`,
            fileName: item.name,
          });
        } else {
          indexData.value.fileAddress.push({ fileAddress: item.url, fileName: item.name });
        }
      }
    });
    indexData.value.workId = props.id;
    workOrderApi.saveWorkOrderDetails(indexData.value).then((res) => {
      message.success('添加工单明细成功');
      if (indexData.value.status == 2) {
        // 工单闭环
        workOrderApi.finishWorkOrder({ id: props.id }).then((res: any) => {
          emit('cancel');
        });
      } else {
        emit('cancel');
      }
    });
  });
};

const beforeUpload = (file: UploadFile) => {
  console.log(file.size);
  const isLt5M = file.size && file.size / 1024 / 1024 < 5;
  if (!isLt5M && isLt5M != 0) {
    message.error('文件大小不能超过5M!');
  }
  return isLt5M;
};
const handleChange = (info: UploadChangeParam) => {
  console.log(info);
  if (info.file.status === 'uploading') {
  }

  if (info.file.status === 'done') {
    // information.value.imgUrl = info.file.response.content.url;
    fileList.value = info.fileList;
    console.log(fileList.value);
  }
  if (info.file.status === 'error') {
    // loading.value = false;
    message.error('上传出错！');
  }
};

// 选择通话记录
const selectPhoneList = () => {
  callHistoryShow.value = true;
};

const cancel = () => {
  callHistoryShow.value = false;
};

// 选择关联的通话记录
const selectCallHistory = (row: any) => {
  indexData.value.callId = row.callid;
  indexData.value.mobile = row.calltype == 7 ? row.calleeno : row.callerno;
  indexData.value.workUsername = row.userVo.username;
  indexData.value.workNum = row.userVo.userNum;
  callHistoryShow.value = false;
};

onMounted(() => {
  if (props.callHistoryInfo) {
    indexData.value.callId = props.callHistoryInfo.callid;
    indexData.value.mobile = props.callHistoryInfo.calltype == 7 ? props.callHistoryInfo.calleeno : props.callHistoryInfo.callerno;
    indexData.value.workUsername = props.callHistoryInfo.userVo.username; 
    indexData.value.workNum = props.callHistoryInfo.userVo.userNum;
  }
});
</script>

<template>
  <!-- 工单明细弹窗 -->
  <h-modal
    v-model:visible="visible"
    :title="'添加明细'"
    :width="600"
    @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    :mask="false"
  >
    <h-form ref="addfrom" :model="indexData" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules">
      <h-form-item @click="selectPhoneList" label="通话记录" name="callId" props="callid">
        <h-input
          :value="indexData.mobile ? indexData.mobile + ' (' + indexData.workUsername + ')' : null"
          style="width: 100%"
        />
      </h-form-item>
      <h-form-item label="明细描述" name="detailsDesc">
        <h-textarea v-model:value="indexData.detailsDesc" placeholder="请输入明细描述" :rows="4" />
      </h-form-item>
      <h-form-item label="状态" name="status">
        <h-select v-model:value="indexData.status" placeholder="请选择工单状态" style="width: 100%">
          <h-select-option :value="1">处理中</h-select-option>
          <h-select-option :value="2">已闭环</h-select-option>
        </h-select>
      </h-form-item>
      <h-form-item label="附件">
        <a-upload-dragger
          v-model:file-list="fileList"
          accept="*"
          :max-count="10"
          class="avatar-uploader"
          :action="'/hbweb/data/hb/common/api/file/upload'"
          :headers="{
            'Hb-Token': token,
          }"
          :multiple="true"
          @change="handleChange"
          :before-upload="beforeUpload"
        >
          <p class="ant-upload-drag-icon">
            <inbox-outlined></inbox-outlined>
          </p>
          <p class="ant-upload-text">点击或将文件拖拽到这里上传</p>
          <p class="ant-upload-hint">支持扩展名：.rar .zip .doc .docx .pdf .jpg...</p>
        </a-upload-dragger>
      </h-form-item>
    </h-form>
    <template #footer>
      <div style="text-align: right">
        <h-button :loading="loading" type="primary" key="back" @click="addDetail">添加</h-button>
      </div>
    </template>
  </h-modal>
  <callHistory
    v-if="callHistoryShow"
    @cancel="cancel"
    @selectCallHistory="selectCallHistory"
    :show="callHistoryShow"
  ></callHistory>
</template>

<style lang="less" scoped>
.important {
  color: red;
}
.imgItem {
  position: relative;
  display: inline-block;
  margin-right: 4px;
  .deleteIcon {
    position: absolute;
    right: 0px;
    z-index: 9999;
  }
}
.Detail {
  margin-left: 55px;
  margin-top: 15px;
  span {
    margin-right: 10px;
  }
  .title {
    font-weight: 600;
    font-size: 15px;
  }
}
</style>
