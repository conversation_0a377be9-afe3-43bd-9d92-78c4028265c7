<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Table as hTable,
  Input as hInput,
  Textarea as hTextarea,
  Switch as hSwitch,
  CheckboxGroup as hCheckboxGroup,
  Checkbox as hCheckbox,
  Image as hImage,
  message,
  Modal,
} from 'ant-design-vue';
import {
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  UploadOutlined,
  SearchOutlined,
  UpOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { computed, ref, watch, onMounted, createVNode } from 'vue';
import { fileApi, workOrderApi } from '@haierbusiness-front/apis';
import type { Ref } from 'vue';
import { workOrderQuery } from '@haierbusiness-front/common-libs';

import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil';
import { HeaderConstant } from '@haierbusiness-front/common-libs';
import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);
const { loginUser } = storeToRefs(applicationStore(globalPinia));
import dayjs from 'dayjs';
import AddDetail from './addDetail.vue';
import callHistory from './callHistory.vue';

interface Props {
  show: boolean;
  data: workOrderQuery;
  knowCenterOptions: any;
  id: number;
  callHistoryInfo: any;
  isLook: boolean;
}
const columns = [
  {
    title: '电话号码',
    dataIndex: 'mobile',
    width: '150px',
  },
  {
    title: '联系人',
    dataIndex: 'workUsername',
    width: '80px',
  },
  {
    title: '工号',
    dataIndex: 'workNum',
    width: '80px',
  },
  {
    title: '处理时间',
    dataIndex: 'detailCreateTime',
    width: '100',
  },
  {
    title: '处理人',
    dataIndex: 'answerName',
    width: '80px',
  },
  {
    title: '处理人工号',
    dataIndex: 'answerNum',
    width: '120px',
  },
  {
    title: '处理明细',
    dataIndex: 'detailsDesc',
    width: '250px',
  },
  {
    title: '附件',
    dataIndex: 'fileAddress',
    width: '120px',
  },
  {
    title: '操作',
    dataIndex: 'opera',
    width: '70px',
  },
];
const props = withDefaults(defineProps<Props>(), {
  show: false,
  callHistoryInfo: null,
  isLook: false,
});

const from = ref();
const spinning = ref<boolean>(false);
const confirmLoading = ref(false);
const callHistoryShow = ref<boolean>(false);
const defaultData: workOrderQuery = {
  mobile: '',
  workName: '',
  userName: '',
  userNum: '',
  personName:'',
  personNum:'',
  businessNum: [""],
  detailsList:[]
};
const Detail = ref<any>({});
const rules: Record<string, Rule[]> = {
  mobile: [{ required: false, message: '请输入来电电话' }],
  workName: [{ required: true, message: '请输入工单名称' }],
  businessNum: [{ required: false, message: '请输入业务单号' }],
};

const indexData: Ref<workOrderQuery> = ref(props.data ? props.data : defaultData);

watch(props, (newValue) => {
  indexData.value = ({ ...newValue.data } as workOrderQuery) || defaultData;
});

const emit = defineEmits(['cancel', 'ok','close']);

const visible = computed(() => props.show);
const showAdd = ref<boolean>(false);
const index = ref<number | null>(null);
// 添加明细
const addDetail = () => {
  // showAdd.value = true;
  Detail.value.detailsList.push({
    fileAddress: [],
    callId: '',
    workUsername: '',
    workNum: '',
    mobile: '',
    status: null,
    answerName:loginUser.value.nickName,
    answerNum:loginUser.value.username,
    detailCreateTime:dayjs(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
  });
};

// 工单闭环
const finishWorkOrder = () => {
  // 根据id 获取详情
  Modal.confirm({
    title: '工单闭环后不能再添加明细，确认要闭环工单并提交吗？',
    icon: createVNode(ExclamationCircleFilled),
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      sumbit(1);
    },
    onCancel: async () => {},
  });
};

// 工单明细提交
const sumbit = (val: any) => {
  from.value
    .validate()
    .then(() => {
      confirmLoading.value = true;
      let obj = JSON.parse(JSON.stringify(Detail.value));
      obj.businessNum = obj.businessNum.join();
      workOrderApi.edit(obj).then((res: any) => {
        if(Detail.value.detailsList&&!Detail.value.detailsList.filter((item:any)=>!item.id).length){
          message.success('保存成功');
          emit('cancel')
        }else{
        Detail.value.detailsList.forEach((item: any, i: number) => {
          if (!item.id) {
            item.fileAddress.forEach((v: any, index: number) => {
              if (
                window.location.origin.indexOf('http://127.0.0.1') !== -1 ||
                window.location.origin.indexOf('http://localhost') !== -1
              ) {
                if (v.response) {
                  item.fileAddress[
                    index
                  ].fileAddress = `https://travel-test.haier.net/${v.response.data.path}`;
                  item.fileAddress[index].fileName = v.name;
                } else {
                  item.fileAddress[index].fileAddress = v.url;
                  item.fileAddress[index].fileName = v.name;
                }
              } else {
                if (v.response) {
                  (item.fileAddress[index].fileAddress = `${window.location.origin}/${v.response.data.path}`),
                    (item.fileAddress[index].fileName = v.name);
                } else {
                  item.fileAddress[index].fileAddress = v.url;
                  item.fileAddress[index].fileName = v.name;
                }
              }
            });
            item.workId = props.id?props.id:res.id;
            workOrderApi.saveWorkOrderDetails(item).then((res) => {
              if (i == Detail.value.detailsList.length - 1) {
                message.success('保存成功');
                emit('cancel')
              }
              getDetail();
            });
          }
        });
        }
        if (val) {
          workOrderApi.finishWorkOrder({ id: props.id?props.id:res.id }).then((res: any) => {
            message.success('工单闭环成功');
            getDetail();
          });
        }
      });
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

const addOne = () => {
  Detail.value.businessNum.push('');
};

const del = (index: number) => {
  Detail.value.businessNum.splice(index, 1);
};

const beforeUpload = (file: UploadFile) => {
  console.log(file.size);
  const isLt5M = file.size && file.size / 1024 / 1024 < 5;
  if (!isLt5M && isLt5M != 0) {
    message.error('文件大小不能超过5M!');
  }
  return isLt5M;
};
const handleChange = (info: UploadChangeParam) => {
  console.log(info);
  if (info.file.status === 'uploading') {
  }

  if (info.file.status === 'done') {
    // information.value.imgUrl = info.file.response.content.url;
    fileList.value = info.fileList;
    console.log(fileList.value);
  }
  if (info.file.status === 'error') {
    // loading.value = false;
    message.error('上传出错！');
  }
};

// 选择通话记录
const selectPhoneList = (i: any) => {
  index.value = i;
  callHistoryShow.value = true;
};

const cancel = () => {
  callHistoryShow.value = false;
};

// 选择关联的通话记录
const selectCallHistory = (row: any, index: number) => {
  console.log(row, index);
  Detail.value.detailsList[index].callId = row.callid;
  Detail.value.detailsList[index].mobile = row.calltype == 7 ? row.calleeno : row.callerno;
  Detail.value.detailsList[index].workUsername = row.userVo.username;
  Detail.value.detailsList[index].workNum = row.userVo.userNum;
  callHistoryShow.value = false;
};

const getDetail = () => {
  // 根据id 获取详情
  if(props.id){
    spinning.value = true
    workOrderApi.getInfoById({ id: props.id }).then((res: any) => {
      res.businessNum = res.businessNum.split(',');
      Detail.value = res;
      if(props.callHistoryInfo){  
        Detail.value.detailsList.push({
            callId:props.callHistoryInfo.callid,
            mobile:props.callHistoryInfo.calltype == 7 ? props.callHistoryInfo.calleeno : props.callHistoryInfo.callerno,
            workUsername:props.callHistoryInfo.userVo.username,
            workNum:props.callHistoryInfo.userVo.userNum,
            fileAddress:[]
        })
      }
      spinning.value = false
    })
    .catch(()=>{
      spinning.value = false
    })
  }else{
     Detail.value = indexData.value
  }
};

//删除明细
const detailsDel = (index:number)=>{
  Detail.value.detailsList.splice(index,1)
}

onMounted(async () => {
  await getDetail();
});
</script>

<template>
  <!-- 工单明细弹窗 -->
  <h-modal
    v-model:visible="visible"
    :title="props.isLook ? '查看工单' :props.id?'编辑工单':'创建工单'"
    :width="1150"
    @cancel="$emit('cancel',1)"
    :confirmLoading="confirmLoading"
  >
  <a-spin tip="加载中..." :spinning="spinning">
    <h-form ref="from" readOnly :model="Detail" :label-col="{ span: 2 }" :wrapper-col="{ span: 18 }" :rules="rules">
      <div v-if="props.isLook">
        <h-form-item label="工单编号">
          {{ Detail?.workTableNum }}
        </h-form-item>
        <h-form-item label="工单标题">
          {{ Detail?.workName }}
        </h-form-item>
        <h-form-item label="工单类型">
          {{ Detail?.workOrderType }}
        </h-form-item>
        <h-form-item label="来电电话">
          {{ Detail?.mobile }}
        </h-form-item>
        <h-form-item label="业务单号">
          {{ Detail?.businessNum?Detail?.businessNum.toString():'' }}
        </h-form-item>
        <h-form-item label="创建人"> {{ Detail?.createName }}（{{ Detail?.createBy }}） </h-form-item>
      </div>
      <div v-if="!props.isLook">
        <h-form-item label="工单标题" name="workName">
          <h-input :maxlength="50" v-model:value="Detail.workName" style="width: 50%" />
        </h-form-item>
        <h-form-item label="工单类型" name="workType">
          <!-- <h-input :maxlength="50" v-model:value="Detail.workOrderType" style="width: 50%" /> -->
          <h-select v-model:value="Detail.workOrderType" style="width: 50%">
            <h-select-option :value="1">投诉</h-select-option>
            <h-select-option :value="2">咨询</h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="业务单号" name="businessNum">
          <div style="margin-bottom: 10px" v-for="(item, index) in Detail.businessNum" :key="index">
            <h-input :maxlength="50" v-model:value="Detail.businessNum[index]" style="width: 42%" /><MinusCircleOutlined
              style="margin: 0 10px"
              @click="del(index)"
              v-if="Detail.businessNum.length != 1"
            /><PlusCircleOutlined
              v-if="index == Detail.businessNum.length - 1"
              @click="addOne"
              style="margin-left: 10px"
            />
          </div>
        </h-form-item>
        <h-form-item label="来电电话" name="mobile">
          <h-input :maxlength="50" v-model:value="Detail.mobile" style="width: 50%" />
        </h-form-item>
        <h-form-item label="来电人姓名" name="userName">
          <h-input :maxlength="50" v-model:value="Detail.userName" style="width: 50%" />
        </h-form-item>
        <h-form-item label="来电人工号" name="userNum">
          <h-input :maxlength="50" v-model:value="Detail.userNum" style="width: 50%" />
        </h-form-item>
      </div>

      <!-- 处理明细 -->
      <div class="detailBox">
        <div class="title">处理明细</div>
        <h-table :pagination="false" :columns="columns" ellipsis :data-source="Detail?.detailsList" bordered>
          <template #emptyText>  暂无数据  </template>
          <template #bodyCell="{ column, text, record, index }">
            <template v-if="column.dataIndex === 'fileAddress'">
              <div v-if="record.id">
                <p v-for="v in record.fileAddress">
                  <a download :href="v.fileAddress" target="_blank">{{ v.fileName }}</a>
                </p>
              </div>
              <div v-else>
                <a-upload
                  v-model:file-list="Detail.detailsList[index].fileAddress"
                  accept="*"
                  :max-count="10"
                  class="avatar-uploader"
                  :action="'/hbweb/data/hb/common/api/file/upload'"
                  :headers="{
                    'Hb-Token': token,
                  }"
                  :multiple="true"
                  @change="handleChange"
                  :before-upload="beforeUpload"
                >
                  <a-button>
                    <upload-outlined></upload-outlined>
                    上传
                  </a-button>
                </a-upload>
              </div>
            </template>

            <template v-if="column.dataIndex === 'mobile'">
              <div v-if="record.id">
                {{ record.mobile }}
              </div>
              <div @click="selectPhoneList(index)" v-else>
                <h-input :value="Detail.detailsList[index].mobile" style="width: 100%" />
              </div>
            </template>
            <template v-if="column.dataIndex === 'detailsDesc'">
              <div style="word-wrap: break-word;width:200px" v-if="record.id">
                {{ record.detailsDesc }}
              </div>
              <div v-else>
                <h-textarea
                  v-model:value="Detail.detailsList[index].detailsDesc"
                  placeholder="请输入明细描述"
                  :rows="2"
                />
              </div>
            </template>
            <template v-if="column.dataIndex === 'opera'">
              <a @click="detailsDel(index)" v-if="!record.id">
                删除 
              </a>
            </template>
          </template>
        </h-table>
        <a-button
          v-if="!props.isLook"
          style="margin-top: 24px"
          type="dashed"
          block
          @click="addDetail"
        >
          <PlusOutlined />
          添加明细
        </a-button>
      </div>
    </h-form>
    </a-spin>
    <template #footer>
      <div v-if="!props.isLook" style="text-align: right">
        <a-button :loading="loading" type="primary" key="back" @click="finishWorkOrder">工单闭环并提交</a-button>
        <a-button :loading="loading" type="primary" key="back" @click="sumbit()">提交</a-button>
      </div>
    </template>
  </h-modal>
  <AddDetail
    v-if="showAdd"
    @cancel="cancel"
    :id="props.id"
    :show="showAdd"
    :callHistoryInfo="props.callHistoryInfo"
  ></AddDetail>
  <callHistory
    v-if="callHistoryShow"
    @cancel="cancel"
    @selectCallHistory="selectCallHistory"
    :index="index"
    :show="callHistoryShow"
  ></callHistory>
</template>

<style lang="less" scoped>
.important {
  color: red;
}
.imgItem {
  position: relative;
  display: inline-block;
  margin-right: 4px;
  .deleteIcon {
    position: absolute;
    right: 0px;
    z-index: 9999;
  }
}
.ant-form-item {
  margin-bottom: 10px;
}
.detailBox {
  margin-left: 20px;
  margin-top: 15px;
  span {
    margin-right: 10px;
  }
  .title {
    font-weight: 600;
    font-size: 15px;
    padding: 10px 0;
  }
}
p {
  margin: 0;
  padding: 0;
}
</style>
