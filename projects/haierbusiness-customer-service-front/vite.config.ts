import { loadEnv } from 'vite'

import vue from '@vitejs/plugin-vue'
import path from 'path';
import {existsSync, mkdirSync, writeFileSync} from 'fs';

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);
  // 生成版本信息插件
  const generateVersionPlugin = {
    name: 'generate-version',
    // 仅在构建时执行
    apply: 'build',
    closeBundle() {
      // 生成版本文件
      const versionInfo = {
        // 使用时间戳作为版本号
        version: Date.now(),
        buildTime: new Date().toISOString(),
        mode: mode
      };

      
            // 确保 dist 目录存在
            const distPath = path.resolve(__dirname, 'dist')
            if (!existsSync(distPath)) {
                mkdirSync(distPath, {recursive: true})
            }

            writeFileSync(
                path.resolve(__dirname, 'dist/version.json'),
                JSON.stringify(versionInfo, null, 2)
            );

    }
  };
  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue(),generateVersionPlugin],
    build:{
      target:['es2015']
    },
    esbuild: {
      charset: 'ascii'
    },
    server: {
      port: 5191,
      proxy: {
        "/hb/aiccrecord/api": {
          target: "https://pre-proxy-aiccrecord.hwork.haier.net/",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
        // "/hb/customer/api": {
        //   target: "http://10.192.50.35:9220/",
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(/^\/hb\/customer\/api/, ""),
        // },

        // 团队票列表本地
        // "/hb/team/api": {
        //   target: "http://10.128.14.48:9223",
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(new RegExp(`/hb/team/api`), ''),
        // },
        
        "/hb": {
          target: "https://travel-test.haier.net/hbweb/callcenter/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
         
      },
      
    }
  }
}
