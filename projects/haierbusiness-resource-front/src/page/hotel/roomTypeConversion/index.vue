<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Popover as hPopover,
  Card as hCard,
  Input as hInput,
  Form as hForm,
  FormItem as hFormItem,
  message,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  QuestionCircleOutlined,
  DeleteOutlined,
  MenuOutlined,
} from '@ant-design/icons-vue';
import { roomApi,hotelApi,addressApi } from '@haierbusiness-front/apis';
import { angtListRes, AddressBookListParams,supplierType } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import EditDialog from './edit-dialog.vue';
import { getCurrentRouter, routerParam } from '@haierbusiness-front/utils';
// import router from '../../../router';
const router = getCurrentRouter();

const currentRouter = ref();

const addressBook = ref<angtListRes[]>([]);
const keyWord = ref<string>('');
const visible = ref<boolean>(false);
const editData = ref<angtListRes>({});
const productList = ref<any>([]);
const searchFrom = ref<any>({
  personNum: '',
  productId: null,
  agentId: '',
});
const columns:any = [
  {
    title: '规则名称',
    dataIndex: 'ruleName',
  },
  {
    title: '供应商',
    dataIndex: 'providerCodeName',
  },
  {
    title: '酒店',
    dataIndex: 'hotelName',
  },
  {
    title: '城市',
    dataIndex: 'cityName',
  },
  {
    title: '区域',
    dataIndex: 'regionName',
  },
  {
    title: '正则表达式',
    dataIndex: 'regularExpression',
  },
  // {
  //   title: '地址',
  //   dataIndex: 'hotelAddress',
  // },
  {
    title: '品牌',
    dataIndex: 'brandName',
  },

  {
    title: '原房型名称',
    dataIndex: 'hotelRoomSourceName',
  },
  {
    title: '目标房型名称',
    dataIndex: 'hotelRoomTargetName',
  },
  {
    title: '规则是否生效',
    dataIndex: 'effectFlag',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '100px',
  },
];
const searchParam = ref<any>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(roomApi.getRoomTransformatRule, {
  manual: false
});


const reset = () => {
  searchParam.value = { }
  listApiRun({
    ...searchParam.value,
    pageNum: current.value,
    pageSize: pageSize.value,
  });
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const handleCreate = () => {
  editData.value={}
  visible.value = true;
};

// 保存或者修改
const handleOk = (object: angtListRes) => {
  roomApi.createRoomTransformatRule(object).then((res) => {
    message.success('保存成功');
    visible.value = false;
    listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  });
  });
};

// 编辑
const edit = (row: angtListRes) => {
  if(row.region){
    row.regionarray = JSON.parse(row.region)
  }
  editData.value = JSON.parse(JSON.stringify(row));
  visible.value = true;
};
// 确认删除
const confirmDel = (row: angtListRes) => {
  roomApi.deleteRoomTransformatRule([row.id]).then((res: any) => {
    message.success('删除成功');
    listApiRun({
    ...searchParam.value,
      pageNum: current.value,
      pageSize: pageSize.value,
    });
  });
};

const onDialogClose = () => {
  editData.value = {};
  visible.value = false;
};


const clear = (type: number) => {
  searchParam.value = {}
  handleTableChange({ current: 1, pageSize: 10 })
}
  // 获取国内城市下拉列表
  const hotelList = ref<any>([])
const getDistrictList = ()=>{
  addressApi.getDistrictList({code:'CN',level:'city'}).then(res=>{
    hotelList.value = res.records
  })
}
const filterOption = (input: string, option: any) => {
  console.log(input,option)
  if(option.name){
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }else{
    return option.brandName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }
};

// 获取区域
const areaList = ref<any>([])
const getAreaList = (value:number)=>{
  addressApi.getDistrictList({code:'CN',level:'district',cityId:value}).then(res=>{
    areaList.value = res.records
  })
}

// 选择完城市 获取区域
const cityChange = (value:number) =>{
  console.log(value)
  if(value){
    getAreaList(value)
  }else{
    areaList.value = []
  }
  searchParam.value.regionId=null
}

// 获取品牌下拉列表
const BrandList = ref<any>([])
const getBrandList = ()=>{
  hotelApi.getHotelBrandProviderMapList({type:2}).then(res=>{
    BrandList.value = res.records
  })
}

// 初始化
onMounted(async () => {
  currentRouter.value = await router;
  listApiRun({
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  });
  getBrandList()
  getDistrictList()
});
</script>

<template>
  <div
    v-if="$route.matched.length < 3"
    style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto"
  >
    <h-card style="margin-bottom: 10px">
      <h-form :labelCol="{span:5, offset: 1}">
        <h-row>
          <h-col :span="6">
            <h-form-item label="名称">
              <h-input allow-clear v-model:value="searchParam.ruleName" placeholder="名称" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="城市">
              <h-select
                ref="city"
                @change="cityChange"
                show-search
                :fieldNames="{label:'name',value:'id'}"
                :options="hotelList"
                :filter-option="filterOption"
                v-model:value="searchParam.cityId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="区域">
              <h-select
                ref="city"
                :disabled="!searchParam.cityId"
                show-search
                :fieldNames="{label:'name',value:'id'}"
                :options="areaList"
                :filter-option="filterOption"
                v-model:value="searchParam.regionId"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
          <!-- <h-col :span="6">
            <h-form-item label="酒店地址">
              <h-input allow-clear v-model:value="searchParam.hotelAddress" placeholder="地址" />
            </h-form-item>
          </h-col> -->
          <h-col :span="6">
            <h-form-item label="酒店名称">
              <h-input
                allow-clear
                v-model:value="searchParam.hotelName"
                placeholder="酒店名称"
              />
            </h-form-item>
          </h-col>
        </h-row>
        <h-row>
          <h-col :span="6">
            <h-form-item label="酒店品牌">
              <h-select
                ref="city"
                show-search
                :fieldNames="{label:'brandName',value:'id'}"
                :options="BrandList"
                :filter-option="filterOption"
                v-model:value="searchParam.hotelBrand"
                style="width: 100%"
                allow-clear
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="是否生效" name="effectFlag">
              <h-select
                ref="select"
                v-model:value="searchParam.effectFlag"
                style="width:100%;"
                allow-clear
              >
              <h-select-option :value="1">是</h-select-option>
              <h-select-option :value="0">否</h-select-option>
              </h-select>
            </h-form-item>
          </h-col>
        </h-row>
      <h-row :align="'middle'">
        <h-col :span="24">
          <h-row :align="'middle'">
            <h-col :span="24" style="text-align: right">
              <h-button style="margin-right:10px;" @click="clear()">重置</h-button>
              <h-button type="primary" style="margin-right: 10px" @click="handleCreate()">
                <PlusOutlined /> 新增
              </h-button>
              <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })"> <SearchOutlined />查询 </h-button>
            
            </h-col>
          </h-row>
        </h-col>
      </h-row>
    </h-form>
    </h-card>
    <h-table :pagination="pagination" :columns="columns" size="small" :loading="loading"  @change="handleTableChange($event as any)"  :data-source="dataSource" bordered>
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'effectFlag'">
          <div>{{ record.effectFlag=='1'?'是':'否' }}
          </div>
        </template>
        <template v-if="column.dataIndex === 'providerCodeName'">
          <div>{{ supplierType[record.providerCode] }}</div>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <div class="editable-row-operations">
            <span>
              <a @click="edit(record)">编辑</a>
              <a-popconfirm
                title="确定要删除吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="confirmDel(record)"
                @cancel="cancel"
              >
                <a class="ml10" href="#">删除</a>
              </a-popconfirm>
            </span>
          </div>
        </template>
      </template>
    </h-table>
    <edit-dialog
      :labelList="productList"
      :knowCenterOptions="knowCenterOptions"
      :show="visible"
      :data="editData"
      @cancel="onDialogClose"
      @ok="handleOk"
    >
    </edit-dialog>
  </div>
  <router-view></router-view>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.ml10 {
  margin-left: 10px;
}

// .pagination {
//   width: 100%;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   margin-top: 20px;
// }
</style>
