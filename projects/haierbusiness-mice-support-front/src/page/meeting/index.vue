<script lang="ts" setup>
import { computed, h, onMounted, onUnmounted, reactive, ref, RendererElement, RendererNode, VNode, watch } from 'vue';
import type { SizeType } from 'ant-design-vue/es/config-provider';
import { useRoute, useRouter } from 'vue-router';
import { meetingAttendeeApi } from '@haierbusiness-front/apis';
import { IMeetingDetails } from '@haierbusiness-front/common-libs';
const currentRouter = ref();
const route = useRoute();
const router = useRouter();
const size = ref<SizeType>('large');
const meetingDetails = ref<IMeetingDetails>({});

const meetingDetail = async () => {
  const response = await meetingAttendeeApi.details(1);
  if (response) {
    meetingDetails.value = response;
  }
  console.log(response, 'meetingDetails.value');
};



const handleClick = (res: number) => {
  switch (res) {
    case 1:
      router.push({
        path: '/support/meeting/edit',
        query: {
          id: meetingDetails?.value.id,
        },
      });
      break;
    case 2:
      router.push({
        path: '/support/attendeeMeeting/index',
        query: {
          id: meetingDetails?.value.id,
        },
      });
      break;
    case 3:
      router.push({
        path: '/support/meeting/agenda',
        query: {
          id: meetingDetails?.value.id,
        },
      });
      break;
    case 4:
      router.push({
        path: '/support/meeting/guest',
        query: {
          id: meetingDetails?.value.id,
        },
      });
      break;
    case 5:
      // 代码块2
      break;
    case 6:
      router.push({
        path: '/support/signIn/index',
        query: {
          miceInfoId: meetingDetails?.value.id,
          miceInfoName: meetingDetails?.value.miceName,
        },
      });
      break;
  }
};

const formatDateRange = (dateStr: string): string => {
  try {
    const [start, end] = dateStr.split('~').map((s) => s.trim());

    // 增强的正则表达式，匹配带或不带时间的日期格式
    const dateRegex = /(\d{4})[年/-](\d{1,2})[月/-](\d{1,2})(?:日|\s|$)/;
    const startParts = start.match(dateRegex);
    const endParts = end.match(dateRegex);

    if (!startParts || !endParts) return dateStr;

    // 如果年份相同
    if (startParts[1] === endParts[1]) {
      // 处理三种可能的日期分隔符：年、/、-
      const separators = ['年', '/', '-'];
      let separatorUsed = '年';
      for (const sep of separators) {
        if (end.includes(sep)) {
          separatorUsed = sep;
          break;
        }
      }

      // 保留原始字符串中的时间部分（如果有）
      return `${start} ~ ${end.replace(new RegExp(`${endParts[1]}\\${separatorUsed}`), '')}`;
    }
    return `${start} ~ ${end}`;
  } catch {
    return dateStr;
  }
};

//会议来源
const meetingScoure = (res: number) => {
  let ScoureName = '';
  if (res) {
    ScoureName = res == 1 ? '会务导入' : '会中创建';
  }

  return ScoureName;
};

// 存储清理函数的ref
const cleanupRef = ref<(() => void) | null>(null);

// 缩放处理函数
const handleResize = () => {
  const demandSubmit = document.getElementById('demand-submit');
  const bottomJump = document.getElementById('bottom-jump');
  if (!demandSubmit || !bottomJump) return;

  if (window.innerWidth <= 1099) {
    demandSubmit.style.transform = 'scale(0.5)';
    bottomJump.style.transform = 'scale(0.5)';
  } else if (window.innerWidth <= 1280) {
    demandSubmit.style.transform = 'scale(0.6)';
    bottomJump.style.transform = 'scale(0.6)';
  } else if (window.innerWidth <= 1536) {
    demandSubmit.style.transform = 'scale(0.8)';
    bottomJump.style.transform = 'scale(0.8)';
    demandSubmit.style.marginTop = '26px';
    bottomJump.style.bottom = '26px';
  } else {
    demandSubmit.style.transform = 'scale(1)';
    bottomJump.style.transform = 'scale(1)';
    demandSubmit.style.marginTop = '0px';
    bottomJump.style.bottom = '74px';
  }
};

// 初始化缩放监听器
const initResizeListener = () => {
  window.addEventListener('resize', handleResize);
  return () => window.removeEventListener('resize', handleResize);
};

onMounted(() => {

  // 初始化缩放监听器
  cleanupRef.value = initResizeListener();

  // 监听窗口大小变化
  watch(
    () => window.innerWidth,
    () => {
      // 清理之前的监听器
      if (cleanupRef.value) {
        cleanupRef.value();
      }
      // 重新设置监听器
      const newCleanup = initResizeListener();
      // 更新清理函数引用
      cleanupRef.value = newCleanup;
    },
  );

  handleResize()

  meetingDetail();


});

// 组件卸载时清理监听器
onUnmounted(() => {
  if (cleanupRef.value) {
    cleanupRef.value();
  }
});
</script>
<template>
  <div class="container">
    <div class="content">
      <div class="main" id="demand-submit">
        <div class="main-top">
          <div class="top-left">
            <h3>{{ meetingDetails?.miceName }}</h3>
          </div>
          <div class="top-right">
            <a-button style="margin-right: 10px">取消会议</a-button>
            <a-button type="primary">完成会议</a-button>
          </div>
        </div>
        <div class="main-middle">
          <a-row :gutter="[16, 16]">
            <a-col :span="8">
              <span class="foot-color">会议名称：</span>{{ meetingDetails?.miceName }}
            </a-col>
            <a-col :span="6"><span class="foot-color">会议来源：</span>{{ meetingScoure(meetingDetails?.miceSource)
            }}</a-col>
            <a-col :span="10" class="flex">
              <span class="foot-color">会议时间：</span>
              <div>{{ formatDateRange(meetingDetails?.miceStartDate + '~' + meetingDetails?.miceEndDate) }}</div>
            </a-col>
          </a-row>
          <a-row :gutter="[16, 16]" class="row-top">
            <a-col :span="8"><span class="foot-color">关联会议：</span>{{ meetingDetails?.miceConnectMeetingId }}</a-col>
            <a-col :span="16"><span class="foot-color">会议酒店：</span>{{ meetingDetails?.miceHotelName }}</a-col>
          </a-row>
          <a-row :gutter="[16, 16]" class="row-top">
            <a-col :span="8"><span class="foot-color">经办人：</span>{{ meetingDetails?.operatorName }}({{
              meetingDetails?.operatorCode }})</a-col>
            <a-col :span="16"><span class="foot-color">会议顾问：</span>{{ meetingDetails?.consultantUsername }}({{
              meetingDetails?.consultantUserCode
            }})</a-col>
          </a-row>
          <!-- <a-row :gutter="[16, 16]" class="row-top">
            <a-col :span="4" style="text-align: right;">会议二维码：</a-col>
            <a-col :span="7" style="color: blue;">点击查看</a-col>
          </a-row> -->
        </div>
        <div class="main-bottom">
          <a-button type="primary" @click="handleClick(1)">
            <img src="@/assets/image/meeting/edit.png" alt="">编辑
          </a-button>
          <a-button @click="handleClick(2)">
            <img src="@/assets/image/meeting/TeamFilled.png" alt="">参会人管理
          </a-button>
          <a-button @click="handleClick(3)">
            <img src="@/assets/image/meeting/MeetingRoomFille.png" alt="">议程管理
          </a-button>
          <a-button>
            <img src="@/assets/image/meeting/TelephoneFilled.png" alt="">现场联系人
          </a-button>
        </div>
      </div>
    </div>
    <div class="bottom-jump" id="bottom-jump">
      <div class="jump-box" @click="handleClick(6)">
        <img src="../../assets/image/meeting/signIn.png" alt="">
        <p>签到管理</p>
      </div>
      <div class="jump-box">
        <img src="../../assets/image/meeting/seatingArrangement.png" alt="">
        <p>座次管理</p>
      </div>
      <div class="jump-box">
        <img src="../../assets/image/meeting/reception.png" alt="">
        <p>全流程接待</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/evaluation.png" alt="">
        <p>评价</p>
      </div>
      <div class="jump-box" @click="handleClick(4)">
        <img src="@/assets/image/meeting/guest.png" alt="">
        <p>嘉宾管理</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/seatSign.png" alt="">
        <p>座位牌打印</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/nameTag.png" alt="">
        <p>胸牌打印</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/Marketing.png" alt="">
        <p>营销微站</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/AI.png" alt="">
        <p>AL助手</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/notice.png" alt="">
        <p>通知管理</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/lotteryDraw.png" alt="">
        <p>抽奖</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/questions.png" alt="">
        <p>现场问答</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/vote.png" alt="">
        <p>投票</p>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
* {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
}

ul,
p {
  margin-bottom: 0;
}

ul li {
  list-style: none;
}

.container {
  width: 100%;
  min-height: 100vh;
  background: url(@/assets/image/meeting/banner.png) no-repeat;
  background-size: 1920px 240px;
  background-position: center top;
}

.content {
  width: 1280px;
  height: auto;
  margin: 0 auto;
  padding-top: 130px;

  .main {
    width: 100%;
    height: 276px;
    background-color: #fff;
    border-radius: 12px;
    padding: 18px 24px;

    .main-top {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 24px;
    }

    .row-top {
      margin-top: 16px;
    }

    .main-middle {
      width: 1232px;
      height: 116px;
      background: #F6F9FC;
      border-radius: 8px;
      padding: 12px;
      color: #1D2129;
    }

    .main-bottom {
      display: flex;
      margin-top: 24px;
      justify-content: left;
      grid-gap: 15px;

      img {
        width: 14px;
        height: 14px;
        margin-right: 3px;
      }
    }
  }
}

.foot-color {
  color: #86909C;
}

.ant-btn {
  border-radius: 4px;
}

.top-left {
  h3 {
    height: 100%;
    line-height: 40px;
    margin-bottom: 0;
  }
}

.bottom-jump {
  width: 1612px;
  height: 138px;
  margin: auto;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 3px 8px 0px rgba(1, 12, 51, 0.07);
  border-radius: 36px;
  border: 1px solid rgba(24, 104, 219, 0.1);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: 72px;
  left: 50%;
  margin-left: -806px;
}

.jump-box {
  max-width: 70px;
  height: 90px;
  text-align: center;
  margin-right: 60px;
  cursor: pointer;

  &:last-child {
    margin-right: 0;
  }

  img {
    width: 60px;
    height: 60px;
    transition:
      transform 0.1s ease,
      transform 0.2s ease 0.1s;
    /* 延迟执行放大效果 */
    transform-origin: center center;

    &:hover {
      transform:
        perspective(1000px) translateY(-15px)
        /* 先执行上移 */
        scale(1.4)
        /* 延迟后执行放大 */
        rotateX(5deg);
    }
  }


  p {
    font-size: 14px;
    margin-top: 10px;
    color: #1D2129;
  }
}
</style>
