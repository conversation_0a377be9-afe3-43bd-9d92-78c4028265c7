<!-- 保证金管理端 -->
<script setup lang="ts">
import {
  Button,
  Col,
  Row,
  Select,
  SelectOption,
  Table,
  Card,
  Statistic,
  RangePicker,
  Input,
  InputNumber,
  Textarea,
  Upload,
  Steps,
  Step,
  Modal,
  Form,
  FormItem,
  Tag,
  Tooltip,
  message,
} from 'ant-design-vue';
import type { TablePaginationConfig, } from 'ant-design-vue/es/table/interface';
import { UploadOutlined, InfoCircleOutlined, CheckCircleOutlined, RightOutlined } from '@ant-design/icons-vue';
import { fileApi, serviceProviderApi } from '@haierbusiness-front/apis';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { bondApi, miceBidManServiceProviderApi } from '@haierbusiness-front/apis';
import { getFileNameFromPath } from '@haierbusiness-front/utils';
import type { BondFilter, Bond, BondDetail } from '@haierbusiness-front/common-libs';
import { FileTypeConstant, getFileType } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted, reactive, h } from 'vue';
import { usePagination } from 'vue-request';
import router from '../../router'


const currentRouter = ref()
const detailData: any = ref([]);
// 获取详情数据
const getServiceProviderDetail = async () => {
  try {
    const res = await serviceProviderApi.get();
    console.log('res', res);
    detailData.value = res.merchantBankRecords;
    if (res) {
      // TODO: 根据接口返回数据结构更新页面数据
      console.log('服务商详情数据:', detailData.value);
    }
  } catch (error) {
    console.error('获取服务商详情失败:', error);
  }
};


onMounted(async () => {
  currentRouter.value = await router
  await Promise.all([
    listApiRun({
      pageNum: 1,
      pageSize: 10,
      // merchantId: 4780
    }),
    getSupplierList(),
    getSummaryData(),
    getServiceProviderDetail()
  ]);
})

const columns: ColumnType[] = [
  {
    title: '单号',
    dataIndex: 'recordNo',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作类别',
    dataIndex: 'type',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      if (text === 1) {
        return h(Tag, { color: 'green' }, () => '保证金缴纳');
      } else if (text === 2) {
        return h(Tag, { color: 'blue' }, () => '保证金退款');
      } else {
        return '-';
      }
    }
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text, record }) => {
      return `${record.type === 1 ? '+' : '-'}${text}元`
    }
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    }
  },
  {
    title: '核对时间',
    dataIndex: 'receiveTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    }
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      switch (text) {
        case 10:
          return '已完成'
        case 11:
          return '已驳回'
        case 20:
          return '核对中'
        default:
          return '-'
      }
    }
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '80px',
    fixed: 'right',
    align: 'center',
    customRender: ({ record }: { record: BondDetail }) => {
      return h(Button, {
        type: 'link',
        onClick: () => showDetail(record)
      }, () => '查看')
    }
  },
];
const searchParam = ref<BondFilter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(bondApi.updateList);

const dataSource = computed(() => (data.value?.records || []));

// 添加保证金汇总数据
const summaryData = ref<any>({});

// 获取保证金汇总数据
const getSummaryData = async () => {
  try {
    const res = await miceBidManServiceProviderApi.getMerchantSummary({
      // merchantId: 4780
    });
    if (res) {
      summaryData.value = res;
    }
  } catch (error) {
    console.error('获取保证金汇总数据失败:', error);
  }
};

// 计算总余额
const totalBalance = computed(() => {
  return Number(summaryData.value?.balance || 0).toFixed(2);
});

// 计算退款中金额
const refundingAmount = computed(() => {
  return Number(summaryData.value?.refundAmount || 0);
});

// 计算核对中笔数
const pendingCount = computed(() => {
  return Number(summaryData.value?.checkAmountCount || 0);
});

// 计算核对中金额
const pendingAmount = computed(() => {
  return Number(summaryData.value?.checkAmount || 0);
});

// 计算已驳回笔数
const rejectedCount = computed(() => {
  return Number(summaryData.value?.rejectCount || 0);
});

// 计算退款中笔数
const refundAmountCount = computed(() => {
  return Number(summaryData.value?.refundAmountCount || 0);
});

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pagination: TablePaginationConfig,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pagination.current || 1,
    pageSize: pagination.pageSize || 10,
  });
};


const beginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => beginAndEnd.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.createStart = n[0]
    searchParam.value.createEnd = n[1]
  } else {
    searchParam.value.createStart = undefined
    searchParam.value.createEnd = undefined
  }
});
const confirmTime = ref<[Dayjs, Dayjs]>()
watch(() => confirmTime.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.receiveStart = n[0]
    searchParam.value.receiveEnd = n[1]
  } else {
    searchParam.value.receiveStart = undefined
    searchParam.value.receiveEnd = undefined
  }
});

const depositModalVisible = ref(false);
const depositForm = reactive({
  amount: undefined,
  remark: '',
  path: '',
  // merchantId: 4780,
  // merchantName: "青岛健力源餐饮管理有限公司"
});

const depositFormRef = ref();
const uploadLoading = ref<boolean>(false);
const submitLoading = ref<boolean>(false);

const depositRules = {
  amount: [{ required: true, message: '请输入缴纳金额', trigger: 'change' }],
  path: [
    {
      required: true,
      message: '请上传支付凭证',
      trigger: 'change',
      validator: (rule: any, value: any) => {
        if (!value) {
          return Promise.reject('请上传支付凭证');
        }
        return Promise.resolve();
      }
    }
  ]
};

const handleDeposit = () => {
  depositModalVisible.value = true;
};

// 清空缴纳表单数据
const resetDepositForm = () => {
  depositForm.amount = undefined;
  depositForm.remark = '';
  depositForm.path = '';
  // 重置上传组件状态
  const uploadEl = document.querySelector('.ant-upload-list');
  if (uploadEl) {
    uploadEl.innerHTML = '';
  }
};

// 上传附件
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadRequest = (options: any) => {
  uploadLoading.value = true;
  submitLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi.upload(formData)
    .then((it) => {
      // 根据当前打开的弹窗来决定更新哪个表单的path
      if (depositModalVisible.value) {
        depositForm.path = baseUrl + it.path;
      } else if (refundModalVisible.value) {
        refundForm.path = baseUrl + it.path;
      }
      options.onProgress(100);
      options.onSuccess(it, options.file);
    })
    .finally(() => {
      uploadLoading.value = false;
      submitLoading.value = false;
    });
};



const handleDepositSubmit = () => {
  submitLoading.value = true;
  depositFormRef.value.validate()
    .then(() => {
      if (!depositForm.path) {
        message.error('请上传支付凭证');
        submitLoading.value = false;
        return;
      }
      const requestParams = {
        ...depositForm,
        path: [depositForm.path],
        type: FileTypeConstant.PAY_PROVE.code
      };
      console.log('保证金缴纳请求参数:', requestParams);
      bondApi.platformAdd(requestParams)
        .then((res) => {
          message.success('保证金缴纳申请提交成功');
          depositModalVisible.value = false;
          // 重置表单数据
          resetDepositForm();
          // 刷新列表和统计数据
          listApiRun({
            pageNum: 1,
            pageSize: 100,
            merchantId: 4780
          });
          // 重新获取保证金汇总数据
          getSummaryData();
        })
        .catch(() => {
          message.error('提交失败，请稍后重试');
        })
        .finally(() => {
          uploadLoading.value = false;
          submitLoading.value = false;
        });
    })
    .catch(() => {
      submitLoading.value = false;
    });
};

const refundModalVisible = ref(false);
const currentStep = ref(0);
const refundForm = reactive({
  amount: undefined as number | undefined,
  remark: '',
  path: '',
  // 账户信息
  merchantName: '',
  merchantId: '',
  bankAccount: '',
  companyPhone: '',
  bankName: '',
  accountHolderName: '',
  bankCountry: '',
  bankBranchAddress: '',
});
// 添加服务商列表
const supplierList = ref<any[]>([]);

// 清空退款表单数据
const resetRefundForm = () => {
  refundForm.amount = undefined;
  refundForm.remark = '';
  refundForm.path = '';
  refundForm.merchantName = '';
  refundForm.merchantId = '';
  refundForm.bankAccount = '';
  refundForm.companyPhone = '';
  refundForm.bankName = '';
  refundForm.accountHolderName = '';
  refundForm.bankCountry = '';
  refundForm.bankBranchAddress = '';
  currentStep.value = 0;
  // 重置上传组件状态
  const uploadEl = document.querySelectorAll('.ant-upload-list');
  if (uploadEl && uploadEl.length > 0) {
    uploadEl.forEach(el => {
      el.innerHTML = '';
    });
  }
};

// 添加分页参数
const supplierPagination = ref({
  current: 1,
  pageSize: 1000,
  total: 0
});

// 获取服务商列表
const getSupplierList = async () => {
  try {
    const res = await bondApi.supplierList({
      pageNum: supplierPagination.value.current,
      pageSize: supplierPagination.value.pageSize
    });
    if (res?.records) {
      supplierList.value = res.records;
      supplierPagination.value.total = res.total || 0;
    } else {
      console.warn('服务商列表数据为空或格式不正确:', res);
    }
  } catch (error) {
    console.error('获取服务商列表失败:', error);
  }
};

// 处理分页变化
const handleSupplierPaginationChange = (page: number, pageSize: number) => {
  supplierPagination.value.current = page;
  supplierPagination.value.pageSize = pageSize;
  getSupplierList();
};

const refundSteps = [
  {
    title: '填写退款信息',
    content: 'refund-info'
  },
  {
    title: '确认退款账户',
    content: 'account-info'
  },
  {
    title: '完成',
    content: 'success'
  }
];

const handleRefund = () => {
  refundModalVisible.value = true;
  currentStep.value = 0;
};

const nextStep = () => {
  if (currentStep.value === 0) {
    // 验证退款金额和退款申请单是否已填写
    refundFormRef.value.validate(['amount', 'path']).then(() => {
      currentStep.value++;
    }).catch(() => {
      // 验证失败，不执行任何操作，表单会自动显示错误信息
    });
  } else if (currentStep.value < refundSteps.length - 1) {
    currentStep.value++;
  }
};

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

const refundSubmitLoading = ref<boolean>(false);

const handleRefundSubmit = async () => {
  refundSubmitLoading.value = true;
  try {
    if (currentStep.value === 0) {
      // 验证第一步表单
      await refundFormRef.value.validate(['amount', 'path']);
      nextStep();
    } else if (currentStep.value === 1) {
      // 验证是否选择了服务商
      if (!refundForm.merchantName) {
        message.error('请选择服务商名称');
        refundSubmitLoading.value = false;
        return;
      }

      // 获取选中的服务商信息
      const selectedSupplier = supplierList.value.find(item => item.id === refundForm.merchantName);

      await bondApi.platformRefund({
        path: [refundForm.path],
        amount: refundForm.amount,
        remark: refundForm.remark,
        type: FileTypeConstant.REFUND_APPLY.code,
        // merchantId: selectedSupplier?.id,
        // merchantName: selectedSupplier?.merchantName
      } as Bond);

      // 提交退款申请成功后刷新列表
      await listApiRun({
        pageNum: 1,
        pageSize: 100,
      });

      // 重新获取保证金汇总数据
      await getSummaryData();

      nextStep();
    }
  } catch (err) {
    console.error('表单验证失败或提交失败:', err);
  } finally {
    refundSubmitLoading.value = false;
  }
};

const closeRefundModal = () => {
  refundModalVisible.value = false;
  resetRefundForm();
};

const refundFormRef = ref();

// 退款金额验证规则
const refundRules = {
  amount: [
    {
      validator: (rule: any, value: any) => {
        if (!value) {
          return Promise.reject(new Error('请输入退款金额'));
        }
        const balance = Number(totalBalance.value);
        if (Number(value) > balance) {
          return Promise.reject(new Error(`退款金额不能大于保证金余额${balance}元`));
        }
        return Promise.resolve();
      },
      trigger: ['change', 'blur']
    }
  ],
  path: [{ required: true, message: '请上传退款申请单', trigger: 'change' }]
};

const detailModalVisible = ref(false);
const currentDetail = ref<BondDetail | null>(null);

const showDetail = async (record: BondDetail) => {
  try {
    const res = await bondApi.get(record.id);
    if (res) {
      currentDetail.value = res;
      detailModalVisible.value = true;
    }
    console.log(currentDetail.value, "currentDetail.value");

  } catch (error) {
    console.error('获取详情失败:', error);
    message.error('获取详情失败');
  }
};

</script>

<template>
  <div class="bond-container">
    <!-- 保证金余额卡片 -->
    <Row :gutter="16" class="bond-row">
      <Col :span="12">
      <Card :bordered="false" class="bond-card">
        <div class="bond-content">
          <div class="bond-amount-section">
            <div class="bond-title">保证金余额</div>
            <span class="bond-number" :precision="2">{{ totalBalance }}元</span>
          </div>

          <div class="bond-info">
            <div class="bond-title" style="margin-bottom: 0;">核对中</div>
            <div class="Modify">
              <span class="bond-number">{{ pendingAmount }}元</span>
              <span class="bond-count">共{{ pendingCount }}笔</span>
            </div>
          </div>
          <div class="bond-button-right" style="margin-top: 4px;">
            <Button type="primary" @click="handleDeposit">新增缴纳</Button>
          </div>
        </div>
      </Card>
      </Col>
      <Col :span="12">
      <Card :bordered="false" class="bond-card" style="padding-bottom: 15px;">
        <div class="bond-content">
          <div class="bond-amount-section">
            <div class="bond-title" style="margin-bottom: 0px;">已驳回</div>
            <span class="bond-count">{{ rejectedCount }}笔</span>
          </div>

          <div class="bond-info">
            <div class="bond-title">退款中</div>
            <div class="Modify">
              <span class="bond-number">{{ refundingAmount }}元</span>
              <span class="bond-count">共{{ refundAmountCount }}笔</span>
            </div>

          </div>
          <div class="bond-button-right">
            <Button type="primary" @click="handleRefund">退款</Button>
          </div>
        </div>

      </Card>
      </Col>
    </Row>

    <!-- 原有的表格内容 -->
    <Row>
      <Col :span="24">
      <Table :columns="columns" :row-key="record => record.id" :data-source="dataSource" :pagination="pagination"
        :loading="loading" @change="handleTableChange" />
      </Col>
    </Row>

    <!-- 缴纳弹窗 -->
    <Modal v-model:open="depositModalVisible" title="保证金缴纳" :footer="null" @cancel="() => {
      depositModalVisible = false;
      resetDepositForm();
    }">
      <Form :model="depositForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" ref="depositFormRef"
        :hide-required-mark="true">
        <FormItem label="缴纳金额" name="amount" :rules="depositRules.amount">
          <InputNumber v-model:value="depositForm.amount" placeholder="请输入缴纳金额" class="bond-input-full" :min="0"
            :precision="2" addonAfter="元" />
        </FormItem>

        <FormItem label="备注" name="remark">
          <Input v-model:value="depositForm.remark" placeholder="请输入" type="textarea" :rows="4" />
        </FormItem>

        <FormItem label="支付凭证" name="path" :rules="depositRules.path">
          <Upload v-model:path="depositForm.path" :custom-request="uploadRequest" :max-count="1"
            accept=".pdf, .doc, .docx, .jpg, .png, .jpeg">
            <Button>
              <upload-outlined></upload-outlined>
              上传
            </Button>
          </Upload>
        </FormItem>
      </Form>
      <div class="step-footer">
        <Button class="bond-button-margin" @click="() => {
          depositModalVisible = false;
          resetDepositForm();
        }">取消</Button>
        <Button type="primary" :loading="submitLoading" @click="handleDepositSubmit">提交</Button>
      </div>
    </Modal>

    <!-- 退款弹窗 -->
    <Modal v-model:open="refundModalVisible" title="保证金退款" :footer="null" @cancel="closeRefundModal">
      <Steps :current="currentStep">
        <Step title="填写退款信息" />
        <Step title="确认退款账户" />
        <Step title="完成" />
      </Steps>

      <!-- 第一步：填写退款信息 -->
      <div v-if="currentStep === 0" class="step-content">
        <Form :model="refundForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" ref="refundFormRef"
          :hide-required-mark="true">
          <FormItem label="退款金额" name="amount" :rules="refundRules.amount">
            <InputNumber v-model:value="refundForm.amount" placeholder="请输入退款金额" class="bond-input-full" :min="0"
              :precision="2" addonAfter="元" @change="() => refundFormRef?.validateFields(['amount'])" />
          </FormItem>

          <FormItem label="备注" name="remark">
            <Input v-model:value="refundForm.remark" placeholder="请输入" type="textarea" :rows="4" />
          </FormItem>

          <FormItem label="退款申请单" name="path" :rules="refundRules.path">
            <Upload v-model:path="refundForm.path" :custom-request="uploadRequest" :max-count="1"
              accept=".pdf, .doc, .docx, .jpg, .png, .jpeg">
              <Button>
                <upload-outlined></upload-outlined>
                上传
              </Button>
            </Upload>
            <div class="upload-tip">
              <a class="download-link">退款申请单模板下载</a>
            </div>
          </FormItem>
        </Form>
        <div class="step-footer">
          <Button type="primary" :loading="refundSubmitLoading || uploadLoading" @click="nextStep">下一步</Button>
        </div>
      </div>

      <!-- 第二步：确认退款账户 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="account-tip">
          <info-circle-outlined />
          <span>请仔细核对退款账户信息，提交申请后将在7个工作日内打到该账户中。</span>
        </div>
        <Form :model="refundForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" :hide-required-mark="true">
          <FormItem label="银行户主">
            <Select allowClear v-model:value="refundForm.accountHolderName" placeholder="请选择银行户主"
              class="bond-input-full" :options="detailData.map(item => ({
                value: item.id,
                label: item.accountHolderName
              }))" :loading="loading" :pagination="supplierPagination" @popupScroll="handleSupplierPaginationChange"
              show-search :filter-option="(input, option) => {
                return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }" @change="async (value) => {
                if (value) {
                  try {
                    const res: any = await serviceProviderApi.get();
                    console.log(detailData, '666');

                    const bankName: any = detailData.find((item: any) => item.id == value);
                    console.log(value, bankName, 'bankName');

                    if (res && bankName) {
                      refundForm.merchantName = res.merchantName
                      refundForm.merchantId = res.unifiedSocialCreditCode;
                      refundForm.accountHolderName = bankName.accountHolderName;
                      refundForm.bankAccount = bankName.accountNumber;
                      refundForm.companyPhone = res.merchantContactsRecords[0].phone;
                      refundForm.bankName = res.merchantBankRecords[0].bankBranchAddress;
                      refundForm.bankCountry = bankName.bankCountry;
                      refundForm.bankBranchAddress = bankName.bankBranchAddress;
                    }
                  } catch (error) {
                    console.error('获取服务商详情失败:', error);
                  }
                } else {
                  // 清空表单
                  refundForm.accountHolderName = '';
                  refundForm.bankAccount = '';
                  refundForm.bankCountry = '';
                  refundForm.bankBranchAddress = '';
                }
              }" />
          </FormItem>
          <!-- <FormItem label="服务商税号">
            <Input v-model:value="refundForm.merchantId" />
          </FormItem> -->
          <FormItem label="银行账号">
            <Input v-model:value="refundForm.bankAccount" disabled />
          </FormItem>
          <!-- <FormItem label="服务商电话">
            <Input v-model:value="refundForm.companyPhone" />
          </FormItem> -->
          <FormItem label="开户行地址">
            <Input v-model:value="refundForm.bankBranchAddress" disabled />
          </FormItem>
          <FormItem label="所属国家">
            <Input v-model:value="refundForm.bankCountry" disabled />
          </FormItem>
        </Form>
        <div class="step-footer">
          <Button class="bond-button-margin" @click="prevStep">上一步</Button>
          <Button type="primary" :loading="refundSubmitLoading" @click="handleRefundSubmit">提交</Button>
        </div>
      </div>

      <!-- 第三步：完成 -->
      <div v-if="currentStep === 2" class="success-content">
        <div class="success-icon">
          <check-circle-outlined />
        </div>
        <div class="success-title">提交成功</div>
        <div class="success-desc">保证金退款申请通过后将在7个工作日内打到退款账户中。</div>
        <div class="account-info">
          <div class="info-item">
            <span class="label">银行户主：</span>
            <span class="value">{{ refundForm.accountHolderName }}</span>
          </div>
          <div class="info-item">
            <span class="label">银行账号：</span>
            <span class="value">{{ refundForm.bankAccount }}</span>
          </div>
          <div class="info-item">
            <span class="label">开户行地址：</span>
            <span class="value">{{ refundForm.bankBranchAddress }}</span>
          </div>
          <div class="info-item">
            <span class="label">所属国家：</span>
            <span class="value">{{ refundForm.bankCountry }}</span>
          </div>
        </div>
        <div class="step-footer">
          <Button type="primary" @click="closeRefundModal">关闭</Button>
        </div>
      </div>
    </Modal>

    <!-- 详情弹框 -->
    <Modal :visible="detailModalVisible" title="保证金详情" @cancel="detailModalVisible = false" :footer="null"
      width="500px">
      <div v-if="currentDetail" class="detail-content">
        <div class="detail-section">
          <div class="detail-item">单号：{{ currentDetail.recordNo }}</div>
          <div class="detail-item">操作类别：{{ currentDetail.type === 1 ? '缴纳' : '退款' }}</div>
          <div class="detail-item">金额：{{ currentDetail.type === 1 ? '+' : '-' }}{{ currentDetail.amount }}元</div>
          <div class="detail-item">创建时间：{{ currentDetail.gmtCreate ? dayjs(currentDetail.gmtCreate).format('YYYY-MM-DD HH: mm: ss') : ' - ' }}</div>
          <div class="detail-item">核对时间：{{ currentDetail.receiveTime ?
            dayjs(currentDetail.receiveTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}</div>
          <div class="detail-item">状态：{{
            currentDetail.state === 10 ? '已完成' :
              currentDetail.state === 11 ? '已驳回' :
                currentDetail.state === 20 ? '核对中' : '-'
          }}</div>
          <div class="detail-item">驳回原因：{{ currentDetail.rejectReason || '-' }}</div>
          <div class="detail-item">
            备注：
            <Tooltip :title="currentDetail.remark" placement="topLeft">
              <span class="remark-text">
                {{ currentDetail.remark || '-' }}
              </span>
            </Tooltip>
          </div>
          <div class="detail-item">
            <div v-if="currentDetail.attachList && currentDetail.attachList.length > 0">
              <div v-for="(item, index) in currentDetail.attachList" :key="index" class="file-item" style="">
                <span class="file-type">{{ getFileType(item.type)?.desc || '附件' }}</span>：
                <a class="value link" :href="item.path" target="_blank">{{ getFileNameFromPath(item.path) }}</a>
              </div>
            </div>
            <span v-else>-</span>
          </div>
        </div>
      </div>
    </Modal>

  </div>
</template>

<style scoped lang="less">
/* 基础容器 */
.bond-container {
  background-color: #ffff;
  height: 100%;
  width: 100%;
  padding: 10px 10px 0px 10px;
  overflow: auto;
}

/* 布局 */
.bond-row {
  margin-bottom: 0px;
}

/* 卡片 */
.bond-card {
  height: 76%;
  background-color: #f0f5ff;

  :deep(.ant-card-body) {
    padding: 16px;
  }
}

/* 内容区域 */
.bond-content {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.bond-amount-section {
  display: flex;
  flex-direction: column;
}

/* 文本样式 */
.bond-title {
  font-size: 18px;
  color: #333;
  font-weight: 500;
}

.bond-number {
  font-size: 16px;
  color: #333;
  font-weight: bold;
}

.bond-count {
  margin-left: 8px;
  font-size: 14px;
  color: #999;
}

.bond-amount {
  :deep(.ant-statistic-content-value) {
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }

  :deep(.ant-statistic-content-suffix) {
    font-size: 14px;
    color: #333;
  }
}

/* 按钮 */
.bond-button-right {
  text-align: right;
}

.bond-button-margin {
  margin-right: 8px;
}

/* 表单 */
.bond-input-full {
  width: 100%;
}

/* 步骤 */
.step-content {
  margin-top: 24px;
  padding: 0 16px;
}

.step-footer {
  margin-top: 24px;
  text-align: right;
}

/* 上传 */
.upload-tip {
  margin-top: 8px;
}

.download-link {
  color: #1890ff;
  cursor: pointer;
}

/* 账户信息 */
.account-tip {
  background-color: #e6f7ff;
  padding: 8px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 成功页面 */
.success-content {
  text-align: center;
  padding: 32px 16px;
}

.success-icon {
  font-size: 52px;
  color: #52c41a;
  margin-bottom: 24px;
}

.success-title {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 16px;
}

.success-desc {
  color: #666;
  margin-bottom: 24px;
}

/* 信息展示 */
.account-info {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  text-align: left;
  margin-bottom: 24px;
}

.info-item {
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

/* 详情弹窗 */
.detail-header {
  text-align: center;
  padding: 24px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 24px;
}

.detail-amount {
  font-size: 32px;
  font-weight: bold;
  color: #000;
  margin-bottom: 8px;
}

.detail-tag {
  display: inline-block;
  padding: 2px 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  color: #666;
}

.detail-content {
  padding: 0 24px;
}

.detail-item {
  margin-bottom: 16px;
  display: flex;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-item .label {
  color: #666;
  width: 80px;
  flex-shrink: 0;
}

.detail-item .value {
  color: #333;
  flex: 1;

  &.link {
    color: #1890ff;
    cursor: pointer;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.detail-footer {
  margin-top: 24px;
  text-align: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}

/* 通用样式 */
.label {
  color: #666;
  margin-right: 8px;
}

.value {
  color: #333;
  font-weight: 500;
}

/* 文件项样式 */
.file-item {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
}

.remark-text {
  display: inline-block;
  max-width: 350px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
</style>
