<script lang="ts" setup>
// 招标大厅
import { ref, reactive, onMounted, onUnmounted, onActivated } from 'vue';
import { ExclamationCircleOutlined, DownOutlined, UnorderedListOutlined } from '@ant-design/icons-vue';
import { Menu, Table, Input, message, Tag, Button } from 'ant-design-vue';
import {
  SearchParams,
  MiceBidManOrderList,
  OrderListResponse,
  OrderListConstant,
  PlatformCount,
  ProcessNode,
  SelTabObj,
  TabList,
  CountSum,
  ApproveListConstant,
  InteractState,
  BidPushState,
} from '@haierbusiness-front/common-libs';
import { schemeApi } from '@haierbusiness-front/apis';
import { resolveParam, routerParam, getDealTime } from '@haierbusiness-front/utils';
import { useRouter } from 'vue-router';
//通知弹框逻辑
import notice from '../component/Modal.vue';
const router = useRouter();

const merchantType = ref<number>(null); // 服务商类型

const current = ref('0');
const items = ref([
  {
    key: '0',
    label: '方案',
    title: '方案',
  },
  {
    key: '1',
    label: '竞价',
    title: '竞价',
  },
]);
const recordCurrent = reactive({
  reason: '',
  miceId: '',
  id: '',
});
const selTabObj = reactive<SelTabObj>({
  selTab1: 0,
  selTab2: 0,
});
const cancel = async () => {
  if (recordCurrent.reason === '') {
    message.error('不参与原因不能为空！');
    return;
  }
  let res;
  if (current.value == '0')
    res = await schemeApi.abstainSubmit({
      miceId: recordCurrent.miceId,
      schemeId: recordCurrent.id,
      abandonReason: recordCurrent.reason,
    });
  else if (current.value == '1')
    res = await schemeApi.abstainBid({
      miceId: recordCurrent.miceId,
      schemeId: recordCurrent.id,
      abandonReason: recordCurrent.reason,
    });
  if (res.success) {
    recordCurrent.reason = '';
    message.success('成功');
    open.value = false;
    getCount();
    getList();
  }
};
const showDealTime = (record) => {
  const now = new Date();
  const interactStartDate = new Date(record.interactStartDate);
  if (now < interactStartDate) {
    let res = getDealTime(record.interactStartDate);
    if (res.length === 8 && res.slice(0, 2) == '00') return res.slice(3, res.length) + '后开始互动';
  }
  return '';
};
const selTab = (num: number | string, type?: string) => {
  columns.value[3].title = current.value[0] == '0' ? '互动截止时间' : '竞价截止时间';

  paramsSearch.pageNum = 1;
  if (selTabObj[type] === num) return;
  if (type) selTabObj[type] = num;
  if (num !== 0) {
    paramsSearch.interactState = num;
    paramsSearch.bidPushState = num;
    getList();
  } else {
    paramsSearch.interactState = undefined;
    paramsSearch.bidPushState = undefined;
    getList();
  }
};
const tabList = ref([]);
const columns = ref([]);
const dataSource = ref([]);
const total = ref(0);
const paramsSearch = reactive({
  merchantId: '',
  mainCode: '',
  interactState: undefined,
  bidPushState: undefined,
  pageNum: 1,
  pageSize: 10,
});
const handleTableChange = (page) => {
  paramsSearch.pageNum = page.current;
  paramsSearch.pageSize = page.pageSize;
  getList();
};
const count0 = ref(0);
const count1 = ref(0);
const getCount = () => {
  getCountTop();

  tabList.value = [];
  let sum = 0;
  let listTab = [];
  if (current.value == '0') {
    listTab = InteractState.toArray();
  } else if (current.value == '1') {
    listTab = BidPushState.toArray();
  }
  listTab.forEach((item) => {
    tabList.value.push({
      name: item.desc,
      interactState: item.code,
      counts: 0,
    });
  });
  tabList.value.forEach(async (item) => {
    let resCount;
    if (typeof item.interactState === 'number')
      if (current.value == '0')
        resCount = await schemeApi.demandCount({
          ...paramsSearch,
          interactState: item.interactState,
        });
      else if (current.value == '1')
        resCount = await schemeApi.pushCount({
          ...paramsSearch,
          bidPushState: item.interactState,
        });

    if (typeof resCount === 'number') {
      item.counts = resCount;
      sum += resCount;
    }
  });
  // tabList.value[0].counts = sum;
};
const timmer = ref(null);
const getList = async () => {
  loading.value = true;
  let resPage;
  if (current.value == '0') {
    columns.value = [
      {
        title: '会议单号',
        dataIndex: 'mainCode',
      },
      {
        title: '会议区域',
        dataIndex: 'cityNames',
      },
      {
        title: '会议时间',
        dataIndex: 'meetingTimeArr',
      },
      {
        title: '互动截止时间',
        dataIndex: 'interactEndDateMinute',
      },
      {
        title: '状态',
        align: 'center',
        dataIndex: 'statusName',
      },
      {
        title: '操作',
        align: 'center',
        dataIndex: 'actions',
      },
    ];
    resPage = await schemeApi.demandPage(paramsSearch);
    total.value = resPage.total;
  } else if (current.value == '1') {
    columns.value = [
      {
        title: '会议单号',
        dataIndex: 'mainCode',
      },
      {
        title: '会议区域',
        dataIndex: 'cityNames',
      },
      {
        title: '竞价ID',
        dataIndex: 'miceSchemeId',
      },
      {
        title: '会议时间',
        dataIndex: 'meetingTimeArr',
      },
      {
        title: '互动截止时间',
        dataIndex: 'interactEndDateMinute',
      },
      {
        title: '状态',
        align: 'center',
        dataIndex: 'statusName',
      },
      {
        title: '操作',
        align: 'center',
        dataIndex: 'actions',
      },
    ];
    resPage = await schemeApi.pushPage(paramsSearch);
    total.value = resPage.total;
  }
  dataSource.value = [];
  resPage.records.forEach((item) => {
    dataSource.value.push({
      ...item,
      meetingTimeArr: item.startDate?.slice(0, 10) + '~' + item.endDate?.slice(0, 10),
      interactEndDateMinute: (item.interactEndDate || item.biddingDeadline)?.slice(0, 16),
      statusName: InteractState.ofType(item.interactState)?.desc || BidPushState.ofType(item.bidPushState)?.desc,
      color: InteractState.ofType(item.interactState)?.color || BidPushState.ofType(item.bidPushState)?.color,
      dealTime: showDealTime(item),
      visible: false,
    });
  });
  loading.value = false;
};
const open = ref(false);
const clearTimmer = () => {
  if (timmer.value) {
    clearInterval(timmer.value);
    timmer.value = null;
  }
};
// 方案互动
const planSub = (record, type: string) => {
  // type - 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
  if (['notBidding', 'biddingView'].includes(type)) {
    biddingBtn(record, type);
    return;
  }
  if (type === 'cancel') {
    recordCurrent.miceId = record.miceId;
    recordCurrent.id = record.id || record.miceSchemeId;
    open.value = true;
    return;
  }
  const recordTemp = {
    miceId: record.miceId,
    interactEndDate: record.interactEndDateMinute, // 方案提报截止时间
    schemeType: type, // view/notReported/reported/schemeView/biddingView
    pdMainId: record.pdMainId, //
    pdVerId: record.pdVerId, //
  };

  router.push({
    path: '/mice-merchant/scheme/demandDetails',
    query: {
      record: routerParam(recordTemp),
    },
  });
};

// 方案竞价
const biddingBtn = (item, type: string) => {
  const params = {
    miceId: item.miceId,
    schemeType: type, // notBidding - 待竞价
    miceSchemeId: item.miceSchemeId, // 方案ID
    pdMainId: item.pdMainId, //
    pdVerId: item.pdVerId, //
  };

  let url = '';

  if (type === 'biddingView') {
    // 查看竞价详情
    url = '/mice-merchant/scheme/schemeBiddingDetail';
  } else {
    // '修改礼品明细' : '方案竞价'
    url =
      merchantType.value === 4 ? '/mice-merchant/scheme/schemePresentChange' : '/mice-merchant/scheme/schemeBinding';
  }

  router.push({
    path: url,
    query: {
      record: routerParam(params),
    },
  });
};

const btnList = (column, text, record, type) => {
  let arr = [];
  if (
    current.value == '0' &&
    ['待提报', '已提报'].includes(record.statusName) &&
    record.dealTime === '' &&
    record.processNode === 'SCHEME_SUBMIT'
  ) {
    arr.push({
      name: '方案互动',
      // name: merchantType.value === 4 ? '修改礼品明细' : '方案互动',
      type: record.statusName == '已提报' ? 'reported' : 'notReported',
    });
  }
  if (current.value == '0' && record.statusName == '已提报' && record.processNode !== 'SCHEME_SUBMIT') {
    arr.push({
      name: '查看方案',
      type: 'schemeView',
    });
  }
  if ((current.value == '0' || (current.value == '1' && merchantType.value == 3)) && record.statusName !== '放弃提报') {
    arr.push({
      name: '查看需求',
      type: 'view',
    });
  }
  if (
    merchantType.value !== 4 &&
    current.value == '1' &&
    record.statusName === '待竞价' &&
    record.processNode === 'BIDDING' &&
    merchantType.value !== 3
  ) {
    arr.push({
      name: '方案竞价',
      type: 'notBidding',
    });
  }
  if (current.value == '1' && ['已竞价', '已中标'].includes(record.statusName)) {
    arr.push({
      name: '查看竞价',
      type: 'biddingView',
    });
  }
  if (
    (['待提报'].includes(record.statusName) && record.dealTime === '' && record.processNode === 'SCHEME_SUBMIT') ||
    (['待竞价'].includes(record.statusName) &&
      record.dealTime === '' &&
      record.processNode === 'BIDDING' &&
      merchantType.value !== 3 &&
      merchantType.value !== 4)
  ) {
    arr.push({
      name: '不参与',
      type: 'cancel',
    });
  }
  // if (type == 'show' && current.value == 0) arr = arr.slice(0, 1);
  // if (type == 'hide' && current.value == 0) arr = arr.slice(1);
  return arr;
};
const updateDealTimes = () => {
  dataSource.value.forEach((item) => {
    item.dealTime = showDealTime(item);
  });
};
const loading = ref(false);
const getData = () => {
  getCount();
  getList();
  if (current.value == '0')
    timmer.value = setInterval(() => {
      updateDealTimes();
    }, 1000);
};

const getUser = async () => {
  // 获取登录服务商的类型
  const res = await schemeApi.getMerchantByUser({});

  // 服务商的类型
  // 1-酒店,2-旅行社,3-保险,4-礼品,5-用车
  merchantType.value = res.merchantType;
};
onActivated(() => {
  getData();
});
onUnmounted(() => {
  clearTimmer();
});
const getCountTop = async () => {
  count0.value = await schemeApi.demandCount({
    ...paramsSearch,
    interactState: 10,
  });
  count1.value = await schemeApi.pushCount({
    ...paramsSearch,
    bidPushState: 10,
  });
};
onMounted(async () => {
  await getUser();
  // await getData();
});
</script>
<template>
  <!-- 招标大厅 -->
  <div class="container">
    <div class="count0"><a-badge size="small" :count="count0" /></div>
    <div class="count1"><a-badge size="small" :count="count1" /></div>

    <a-tabs
      style="margin-left: 20px"
      v-model:activeKey="current"
      @change="
        () => {
          selTab(0, 'selTab1');
          getData();
        }
      "
    >
      <a-tab-pane v-for="item in items" :key="item.key" :tab="item.label"> </a-tab-pane>
    </a-tabs>

    <div class="header">
      <div class="status-bar">
        <div :class="'status-item ' + (selTabObj.selTab1 === 0 ? 'active' : '')" @click="selTab(0, 'selTab1')">
          <span>全部订单</span>
        </div>
        <div
          v-for="(item, index) in tabList"
          :key="item.interactState"
          :class="'status-item ' + (selTabObj.selTab1 === item.interactState ? 'active' : '')"
          @click="selTab(item.interactState, 'selTab1')"
        >
          <span>{{ item.name }}</span>
          <span class="order-count">{{ item.counts }}</span>
        </div>
      </div>
      <div>
        <a-input
          :maxlength="200"
          allowClear
          v-model:value="paramsSearch.mainCode"
          placeholder="请输入会议单号"
          @pressEnter="
            () => {
              paramsSearch.pageNum = 1;
              getData();
            }
          "
          style="width: 200px; margin-right: 10px"
          size="small"
        ></a-input>
        <a-button
          @click="
            () => {
              paramsSearch.pageNum = 1;
              getData();
            }
          "
          size="small"
          type="primary"
          >搜索</a-button
        >
      </div>
    </div>
    <div class="talbe-list">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="{
          total: total,
          current: paramsSearch.pageNum,
          pageSize: paramsSearch.pageSize,
        }"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, text, record }">
          <div v-if="column.dataIndex === 'actions'">
            <a-popover
              placement="bottom"
              arrow-point-at-center
              v-model:open="record.visible"
              trigger="hover"
              v-if="
                (current == '0' &&
                  ['待提报', '已提报'].includes(record.statusName) &&
                  record.dealTime === '' &&
                  record.processNode === 'SCHEME_SUBMIT') ||
                (current == '0' && record.statusName == '已提报' && record.processNode !== 'SCHEME_SUBMIT') ||
                (['待提报'].includes(record.statusName) &&
                  record.dealTime === '' &&
                  record.processNode === 'SCHEME_SUBMIT') ||
                (current == '1' && btnList(column, text, record, 'hide').length > 0)
              "
            >
              <template #content>
                <a-button
                  type="link"
                  style="display: block"
                  v-for="item in btnList(column, text, record, 'hide')"
                  :key="item.name"
                  @click="planSub(record, item.type)"
                  >{{ item.name }}</a-button
                >
              </template>
              <a-button type="link">操作</a-button>
            </a-popover>
          </div>
          <div v-else-if="column.dataIndex === 'statusName'">
            <a-tag :color="record.color">{{ record[column.dataIndex] }}</a-tag>
            <a-tooltip placement="top" v-if="['放弃提报', '放弃竞价'].includes(record.statusName)">
              <template #title>原因：{{ record.abandonReason }} </template>
              <ExclamationCircleOutlined style="color: red" />
            </a-tooltip>
            <span class="dealtime">
              {{ record.dealTime }}
            </span>
          </div>
          <div v-else>
            {{ record[column.dataIndex] }}
          </div>
        </template>
      </a-table>
    </div>
    <a-modal v-model:open="open" title="不参与原因" @ok="cancel">
      <a-textarea v-model:value="recordCurrent.reason" :rows="4" :maxLength="200" placeholder="请输入不参与原因" />
    </a-modal>
  </div>
</template>
<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  .header {
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .status-bar {
    margin-top: 10px;
    // 状态切换栏样式，显示全部订单、待处理等状态
    display: flex;
    // gap: 8px;
    :first-child {
      border-radius: 4px 0 0 4px;
    }
    :last-child {
      border-radius: 0 4px 4px 0;
    }
    .status-item {
      white-space: nowrap;
      padding: 6px 10px;
      cursor: pointer;
      display: flex;
      align-items: center;
      color: #666;
      font-size: 14px;
      background: #f2f3f5;

      &.active {
        .order-count {
          border: none;
          // width: 20px;
        }
        border-radius: 4px;
        margin: 0 14px;
        background: #1868db;
        color: white;
      }
    }
  }
  .talbe-list {
    padding: 20px 10px;
  }
}
.dealtime {
  color: #ff0000;
}
.more {
  cursor: pointer;
  margin-left: 10px;
  display: inline-block;
  // border: 1px solid #ccc;
  border-radius: 5px;
  padding: 0px 5px;
  width: 60px;
  height: 24px;
}
.more:hover {
  .anticon {
    color: #1677ff;
  }
}
:deep(.ant-tabs-nav-list) {
  .ant-tabs-tab-btn {
    position: relative;
    // margin-left: 20px;
    font-size: 16px;
  }
  font-weight: 700;
}
.count0 {
  position: absolute;
  top: 5px;
  left: 49px;
}

.count1 {
  position: absolute;
  top: 5px;
  left: 112px;
}
</style>
