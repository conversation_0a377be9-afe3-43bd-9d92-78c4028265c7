# 打包路径
VITE_BASE_URL = ./
VITE_BUSINESSTRAVEL_URL=https://businesstravel-test.haier.net/
VITE_BUSINESS_URL=https://travel-test.haier.net/

VITE_BUSINESS_INDEX_URL=http://localhost:5183/
VITE_BUSINESS_TRIP_URL=http://localhost:5186/

VITE_MICE_BID_URL=http://localhost:5182/
VITE_MICE_MERCHANT_URL=https://travel-test.haier.net/hbweb/mice-merchant/
VITE_MICE_BIDMAN_URL=https://travel-test.haier.net/hbweb/mice-bidman/
VITE_BUSINESS_TEAM_URL=http://localhost:5201/

VITE_BUSINESS_PROCESS_URL=http://127.0.0.1:5178/
VITE_BUSINESS_TEAM_URL=https://travel-test.haier.net/hbweb/team/


# 出差申请单跳转第三方单点登录url
VITE_BUSINESS_TRIP_SINGLE=https://travelservice-test.haier.net/

# EES报销单点链接
VITE_BUSINESS_EES_SINGLE=https://ygfytest.haier.net/ecs-console/api/newTravelBillSingleSignOn

VITE_BUSINESS_HELPER_URL=https://travel-test.haier.net/hbweb/helper/

# 测试水印
VITE_TEST_WATERMARK = '测试系统'

VITE_JD_PICTURE_URL = 'http://img13.360buyimg.com/n1/'

#健康检查IP端口
VITE_HEALTH_PORT=https://**************:8043/agentgateway/resource/health.jsp

#健康检查IP端口
VITE_WEBSOCKET_URL=wss://**************:8043/agentgateway/ccgateway/agent/