<script setup lang="ts">
import {
  Dropdown as hDropdown,
  Tree as hTree,
  InputSearch as hInputSearch,
  Popover as hPopover,
  Spin as hSpin,
  Card as hCard,
  DatePicker as hDatePicker,
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Table as hTable,
  BreadcrumbItem as hBreadcrumbItem,
  Breadcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  LayoutContent as hLayoutContent,
  LayoutHeader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  Menu as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message,
  TableProps,
} from 'ant-design-vue';
import { computed, onMounted, ref, watch } from 'vue';
import {
  DeleteOutlined,
  LinkOutlined,
  DownOutlined,
  UngroupOutlined,
  FolderOutlined,
  FileOutlined,
  NodeExpandOutlined,
  HomeOutlined,
  HolderOutlined,
  FileTextOutlined,
  ArrowUpOutlined,
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';
import { groupApi, roleApi, resourceApi, userApi, applicationApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import {
  IGroupDeleteRequest,
  IGroupInfo,
  IGroupSaveUpdateRequest,
  IPageResponse,
  IResourceInfoTreeResponse,
  IResourceTreeNode,
  IRoleDeleteRequest,
  IRoleInfo,
  IRoleSaveUpdateRequest,
  IUserInfo,
  IUserListRequest,
  IUserSaveUpdateRequest,
  ResourceTypeConstant
} from '@haierbusiness-front/common-libs';
import { useScroll } from '@vueuse/core';
import { DataNode } from 'ant-design-vue/lib/tree';
import { Key } from 'ant-design-vue/lib/table/interface';
import { reject } from 'lodash';
const visibleRoleSearch = ref();
const roleSearchName = ref();
const roleName = ref();
const roleMenu = ref();
const startscoll = ref();
const roleData = ref<IRoleInfo[]>([]);
const selectedRoleKeys = ref();
const roleMenuRightClick = (item: IRoleInfo) => {
  selectedRoleKeys.value = [item.id];
  onSelectRole({ key: item.id });
};

const { run: roleDeleteRun, loading: roleDeleteLoading } = useRequest(roleApi.delete, {
  onSuccess: (data: void, param: [IRoleDeleteRequest]) => {
    message.success('成功');
    roleData.value = roleData.value.filter((it) => it.id !== param[0].id);
  },
});

const onContextRoleDeleteClick = (item: IRoleInfo) => {
  roleDeleteRun({ id: item.id });
};

const onSelectRole = (item: any) => {
  resourceTreeRun({
    roleId: item.key,
    parentId: 0,
  });
};

const {
  data: currentRoleData,
  run: roleListApiRun,
  loading: roleListLoading,
  current,
  pageSize,
  totalPage,
} = usePagination(roleApi.list, {
  manual: false,
  defaultParams: [
    {
      pageSize: 999,
    },
  ],
  onBefore: () => {
    startscoll.value = false;
  },
  onSuccess: (data: IPageResponse<IRoleInfo>) => {
    if (data?.records) {
      roleData.value.push(...data?.records);
      /* console.log("selectedRoleKeys.value",selectedRoleKeys.value)
            if (!selectedRoleKeys.value) {
                selectedRoleKeys.value = [currentRoleData.value?.records[0].id]
            } */
    }
    startscoll.value = true;
  },
});

const handleRoleSearch = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  roleListApiRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
    name: roleName.value,
  });
};

const searchRoleByName = () => {
  roleName.value = roleSearchName.value;
  visibleRoleSearch.value = false;
  roleData.value = [];
  handleRoleSearch({ current: 1, pageSize: 999 });
};

const handleRoleHoverChange = (visible: boolean) => {
  visibleRoleSearch.value = visible;
};

const { arrivedState } = useScroll(roleMenu);

watch(arrivedState, (newValue, oldValue) => {
  if (startscoll.value && newValue.bottom && current.value < totalPage.value) {
    console.log('到底了');
    handleRoleSearch({ current: current.value + 1, pageSize: pageSize.value });
  }
});

// 新增组
const roleNewForm = ref();
const newRoleFormModel = ref<IRoleSaveUpdateRequest>({});
const visibleNewRole = ref(false);
const gotoNewRole = () => {
  visibleNewRole.value = true;
};
const newRoleHandleOk = () => {
  roleSaveRun(newRoleFormModel.value);
};
const { run: roleSaveRun, loading: roleSaveLoading } = useRequest(roleApi.save, {
  onSuccess: () => {
    message.success('成功');
    roleData.value = [];
    visibleNewRole.value = false;
    handleRoleSearch({ current: 1, pageSize: pageSize.value });
  },
});

// 资源查询
const {
  data: resourceTreeData,
  run: resourceTreeRun,
  loading: resourceTreeLoading,
} = useRequest(resourceApi.searchTrees, {});

const resourceTree = computed(() => {
  if (resourceTreeData.value) {
    return [
      {
        id: 0,
        name: 'ROOT',
        type: 0,
        children: [...resourceTreeData.value],
      },
    ];
  } else {
    return [
      {
        id: 0,
        name: 'ROOT',
        type: 0,
        children: [],
      },
    ];
  }
});
const checkedLinkTreeKeys = ref<{ checked: number[]; halfChecked: number[] }>({ checked: [], halfChecked: [] });
const flatTreeKeys = ref<number[]>([]);
const flatTreeKey = (tree: IResourceInfoTreeResponse[]) => {
  for (let i of tree) {
    if (i.id != undefined && i.id != null) {
      if (i.id) {
        flatTreeKeys.value.push(i.id);
      }
      if (i.children && i.children.length > 0) {
        flatTreeKey(i.children);
      }
    }
  }
};
const selectedTreeKeys = ref();
// 资源关联
const visibleLinkResource = ref();
const gotoLinkResource = () => {
  flatTreeKeys.value = [];
  flatTreeKey(resourceTree.value);
  visibleLinkResource.value = true;
  resourceLinkTreeRun({
    parentId: 0,
  });
};
const {
  data: resourceLinkTreeData,
  run: resourceLinkTreeRun,
  loading: resourceLinkTreeLoading,
} = useRequest(resourceApi.searchTrees, {
  onSuccess: (data: any) => {
    generateKey(data);
    console.log(flatTreeKeys.value);
    checkedLinkTreeKeys.value.checked = flatTreeKeys.value;
  },
});

const generateKey = (resource: IResourceTreeNode[]) => {
  for (let i of resource) {
    i.key = i.id;
    if (i.children && i.children.length > 0) {
      generateKey(i.children);
    }
  }
};
const resourceLinkTree = computed(() => {
  if (resourceLinkTreeData.value) {
    return [
      {
        id: 0,
        key: 0,
        name: 'ROOT',
        type: 0,
        children: [...resourceLinkTreeData.value],
      },
    ];
  } else {
    return [
      {
        id: 0,
        key: 0,
        name: 'ROOT',
        type: 0,
        children: [],
      },
    ];
  }
});
const selectedLinkTreeKeys = ref();
const linkResource = () => {
  linkResourceRun({
    roleId: selectedRoleKeys.value[0],
    resourceIds: checkedLinkTreeKeys.value.checked,
  });

  visibleLinkResource.value = false;
};

const {
  data: linkResourceData,
  run: linkResourceRun,
  loading: linkResourceLoading,
} = useRequest(roleApi.linkResource, {
  onSuccess: () => {
    resourceTreeRun({
      roleId: selectedRoleKeys.value[0],
      parentId: 0,
    });
    message.success('成功');
  },
});
</script>

<template>
  <h-modal v-model:open="visibleNewRole" :title="'新增角色'" :confirm-loading="roleSaveLoading" @ok="newRoleHandleOk">
    <h-form ref="RoleNewForm" :model="newRoleFormModel" :label-col="{ span: 5 }" :wrapper-col="{ span: 14 }">
      <h-form-item label="名称" name="name" :rules="[{ required: true, message: '请输入角色名!' }]">
        <h-input v-model:value="newRoleFormModel.name" />
      </h-form-item>
      <h-form-item label="描述" name="description">
        <h-input v-model:value="newRoleFormModel.description" />
      </h-form-item>
    </h-form>
  </h-modal>
  <h-modal
    v-model:open="visibleLinkResource"
    :title="'关联资源'"
    :confirm-loading="resourceLinkTreeLoading"
    style="width: 1200px; height: 1000px"
    @ok="linkResource"
  >
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="searchEnterprise">企业：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchEnterprise" placeholder="" autocomplete="off" />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="searchName">账号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchName" placeholder="" autocomplete="off" />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="searchNickName">姓名：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchNickName" placeholder="" autocomplete="off" />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="searchState">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="searchState" placeholder="" autocomplete="off" />
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-tree
          :checkStrictly="true"
          v-if="resourceLinkTree.length"
          :defaultExpandAll="true"
          :show-line="{ showLeafIcon: false }"
          checkable
          :show-icon="true"
          :tree-data="resourceLinkTree as unknown as DataNode[]"
          v-model:selectedKeys="selectedLinkTreeKeys"
          v-model:checkedKeys="checkedLinkTreeKeys">
          <template #icon="{ dataRef }">
             <template v-if="dataRef.type === 0">
                <HomeOutlined />
              </template>
              <template v-if="dataRef.type === ResourceTypeConstant.INTERFACE.type"
                ><!-- 接口 -->
                <NodeExpandOutlined />
              </template>
              <template v-if="dataRef.type === ResourceTypeConstant.PAGE.type"
                ><!-- 页面 -->
                <FileOutlined />
              </template>
              <template v-if="dataRef.type === ResourceTypeConstant.PAGE_MENU.type"
                ><!-- 页面(菜单) -->
                <FileTextOutlined />
              </template>
              <template v-if="dataRef.type === ResourceTypeConstant.PAGE_GROUP.type"
                ><!-- 页面组 -->
                <FolderOutlined />
              </template>
              <template v-if="dataRef.type === ResourceTypeConstant.WIDGET.type"
                ><!-- 组件 -->
                <UngroupOutlined />
              </template>
              <template v-if="dataRef.type === ResourceTypeConstant.MANAGE_APPLICATION.type"
                ><!-- 应用（管理） -->
                <HolderOutlined />
              </template>
              <template v-if="dataRef.type === ResourceTypeConstant.APPLICATION.type"
                ><!-- 应用 -->
                <AppstoreOutlined />
              </template>
          </template>
          <template #title="{ dataRef }">
            <span>{{ dataRef.name }}</span>
          </template>
          <template #switcherIcon="{ switcherCls }">
            <DownOutlined :class="switcherCls" />
          </template>
        </h-tree>
      </h-col>
    </h-row>
  </h-modal>

  <h-row style="height: 100%; width: 100%">
    <h-col :span="4" style="height: 100%">
      <div
        style="
          height: 40px;
          border-right: 10px solid #f0f2f5;
          padding: 5px 5px 10px 20px;
          font-size: 18px;
          font-weight: 600;
          border-bottom: 1px solid #f0f0f0;
          background-color: #ffff;
        "
      >
        角色维护
        <h-popover
          title="名称检索"
          placement="bottom"
          trigger="click"
          :visible="visibleRoleSearch"
          @visibleChange="handleRoleHoverChange"
        >
          <template #content>
            <h-input-search
              v-model:value="roleSearchName"
              enter-button
              allow-clear
              @search="searchRoleByName"
              autocomplete="off"
            />
          </template>
          <h-button type="primary" shape="circle" style="float: right">
            <template #icon>
              <SearchOutlined />
            </template>
          </h-button>
        </h-popover>
        <h-button type="primary" shape="circle" style="float: right; margin-right: 3px" @click="gotoNewRole">
          <template #icon>
            <PlusOutlined />
          </template>
        </h-button>
      </div>
      <div style="height: calc(100% - 40px); border-right: 10px solid #f0f2f5; position: relative">
        <div
          v-if="roleListLoading"
          style="float: left; position: absolute; height: 100%; width: 100%; text-align: center; padding-top: 100%"
        >
          <h-spin />
        </div>
        <h-menu
          v-if="roleData"
          v-model:selectedKeys="selectedRoleKeys"
          @click="onSelectRole"
          style="height: 100%; width: 100%; border: none; overflow-x: hidden; overflow-y: auto"
          mode="inline"
          ref="roleMenu"
        >
          <h-menu-item :key="i.id" v-for="i of roleData" @contextmenu.prevent.stop="roleMenuRightClick(i)">
            <h-dropdown :trigger="['contextmenu']" placement="bottomLeft">
              <div style="width: 100%">{{ i.name }}</div>
              <template #overlay>
                <h-menu @click="onContextRoleDeleteClick(i)">
                  <h-menu-item> <DeleteOutlined />&nbsp;&nbsp;&nbsp;删除&nbsp;&nbsp; </h-menu-item>
                </h-menu>
              </template>
            </h-dropdown>
          </h-menu-item>
        </h-menu>
      </div>
    </h-col>

    <h-col :span="20" style="height: 100%">
      <div
        style="
          height: 40px;
          padding: 5px 20px 10px 20px;
          font-size: 18px;
          font-weight: 600;
          border-bottom: 1px solid #f0f0f0;
          background-color: #ffff;
        "
      >
        角色资源
        <h-button type="primary" shape="circle" style="float: right; margin-right: 3px" @click="gotoLinkResource">
          <template #icon>
            <LinkOutlined />
          </template>
        </h-button>
      </div>
      <div style="height: calc(100% - 40px); overflow-y: auto; padding: 5px 20px 10px 20px; background-color: #ffff">
        <h-tree
          v-if="resourceTree.length"
          :defaultExpandAll="true"
          :show-line="{ showLeafIcon: false }"
          :show-icon="true"
          :tree-data="resourceTree as unknown as DataNode[]"
          v-model:selectedKeys="selectedTreeKeys"
        >
          <template #icon="{ dataRef }">
            <template v-if="dataRef.type === 0">
              <HomeOutlined />
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.INTERFACE.type"
              ><!-- 接口 -->
              <NodeExpandOutlined />
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.PAGE.type"
              ><!-- 页面 -->
              <FileOutlined />
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.PAGE_MENU.type"
              ><!-- 页面(菜单) -->
              <FileTextOutlined />
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.PAGE_GROUP.type"
              ><!-- 页面组 -->
              <FolderOutlined />
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.WIDGET.type"
              ><!-- 组件 -->
              <UngroupOutlined />
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.MANAGE_APPLICATION.type"
              ><!-- 应用（管理） -->
              <HolderOutlined />
            </template>
            <template v-if="dataRef.type === ResourceTypeConstant.APPLICATION.type"
              ><!-- 应用 -->
              <AppstoreOutlined />
            </template>
          </template>
          <template #title="{ dataRef }">
            <span>{{ dataRef.name }}</span>
          </template>
          <template #switcherIcon="{ switcherCls }">
            <DownOutlined :class="switcherCls" />
          </template>
        </h-tree>
      </div>
    </h-col>
  </h-row>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}
</style>
