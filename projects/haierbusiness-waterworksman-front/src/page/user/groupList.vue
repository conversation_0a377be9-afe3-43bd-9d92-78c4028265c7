<script setup lang="ts">

import { Dropdown as hDropdown, Tree as hTree, InputSearch as hInputSearch, Popover as hPopover, Spin as hSpin, Card as hCard, DatePicker as hDatePicker, Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem, Input as hInput, Table as hTable, BreadcrumbItem as hBreadcrumbItem, Breadcrumb as hBreadcrumb, LayoutSider as hLayoutSider, LayoutFooter as hLayoutFooter, LayoutContent as hLayoutContent, LayoutHeader as hLayoutHeader, Layout as hLayout, MenuItem as hMenuItem, MenuItemGroup as hMenuItemGroup, SubMenu as hSubMenu, Menu as hMenu, Divider as hDivider, Space as hSpace, Button as hButton, Col as hCol, Result as hResult, Row as hRow, TabPane as hTabPane, Tabs as hTabs, message, TableProps } from 'ant-design-vue';
import { computed, onMounted, ref, watch } from 'vue';
import { DeleteOutlined, LinkOutlined, AddCircleFilled, DownOutlined, UngroupOutlined, FolderOutlined, FileOutlined, NodeExpandOutlined, HomeOutlined, ArrowDownOutlined, EnterOutlined, ArrowUpOutlined, UploadOutlined, UserOutlined, NotificationOutlined, AppstoreOutlined, MenuFoldOutlined, MenuUnfoldOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { groupApi, roleApi, resourceApi, userApi, applicationApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { IGroupDeleteRequest, IGroupInfo, IGroupSaveUpdateRequest, IPageResponse, IRoleInfo, IUserInfo, IUserListRequest, IUserSaveUpdateRequest,RoleSystemConstant,UserGroupSystemConstant } from '@haierbusiness-front/common-libs';
import { useScroll } from '@vueuse/core'
import { DataNode } from 'ant-design-vue/lib/tree';
import { Key } from 'ant-design-vue/lib/table/interface';
import { reject } from 'lodash';

import { checkUserGroups } from '@haierbusiness-front/utils/src/authorityUtil'

const visibleGroupSearch = ref()
const groupSearchName = ref()
const groupName = ref()
const groupMenu = ref()
const startscoll = ref()
const groupData = ref<IGroupInfo[]>([])
const selectedGroupKeys = ref()
const groupMenuRightClick = (item: IGroupInfo) => {
    selectedGroupKeys.value = [item.id]
    onSelectGroup({ "key": item.id })
}

const {
    run: groupDeleteRun,
    loading: groupDeleteLoading,
} = useRequest(groupApi.delete, {
    onSuccess: (data: void, param: [IGroupDeleteRequest]) => {
        message.success("成功");
        groupData.value = groupData.value.filter(it => it.id !== param[0].id);

    },
});

const onContextGroupDeleteClick = (item: IGroupInfo) => {
    groupDeleteRun({ "id": item.id })
}

const onSelectGroup = (item: any) => {
    roleData.value = [{ "id": 0, "name": "全部" }]
    selectedRoleKeys.value = [0]
    roleListApiRun({    
        "groupId": item.key
    })
    resourceTreeRun({
        "groupId": item.key,
        "parentId": 0,
    })
    userListParams.value.groupId =  item.key
    handleUserSearch(
        {
            current: 1, pageSize: 10,
        } 
    )
}

const {
    data: currentGroupData,
    run: groupListApiRun,
    loading: groupListLoading,
    current,
    pageSize,
    totalPage,
} = usePagination(groupApi.list, {
    manual: false,
    defaultParams: [
        {
            pageSize: 999
        }
    ],
    onBefore: () => {
        startscoll.value = false
    },
    onSuccess: (data: IPageResponse<IGroupInfo>) => {
        if (data?.records) {
            groupData.value.push(...(data?.records))
        }
        startscoll.value = true
    }
});



const handleLinkRoleSearch = (
    pag: { current: number; pageSize: number },
    filters?: any,
    sorter?: any,
) => {
    linkRoleListRun({
        pageNum: pag.current,
        pageSize: pag.pageSize,
        name: groupName.value,
    });
};

const handleGroupSearch = (
    pag: { current: number; pageSize: number },
    filters?: any,
    sorter?: any,
) => {
    groupListApiRun({
        pageNum: pag.current,
        pageSize: pag.pageSize,
        name: groupName.value,
    });
};

const searchGroupByName = () => {
    groupName.value = groupSearchName.value
    visibleGroupSearch.value = false
    groupData.value = []
    handleGroupSearch({ current: 1, pageSize: 999 })
}

const handleGroupHoverChange = (visible: boolean) => {
    visibleGroupSearch.value = visible
};

const { arrivedState } = useScroll(groupMenu)

watch(arrivedState, (newValue, oldValue) => {
    if (startscoll.value && newValue.bottom && current.value < totalPage.value) {
        handleGroupSearch({ current: current.value + 1, pageSize: pageSize.value })
    }
});

// 新增组
const groupNewForm = ref()
const newGroupFormModel = ref<IGroupSaveUpdateRequest>({})
const visibleNewGroup = ref(false)
const gotoNewGroup = () => {
    visibleNewGroup.value = true
}
const newGroupHandleOk = () => {
    groupSaveRun(newGroupFormModel.value)
}
const {
    run: groupSaveRun,
    loading: groupSaveLoading,
} = useRequest(groupApi.save, {
    onSuccess: () => {
        message.success("成功");
        groupData.value = []
        visibleNewGroup.value = false;
        handleGroupSearch({ current: 1, pageSize: pageSize.value })
    },
});

// 角色查询
const visibleRoleSearch = ref()
const roleSearchName = ref()
const roleName = ref()
const roleMenu = ref()
const startRolescoll = ref()
const roleData = ref<IRoleInfo[]>([])
const selectedRoleKeys = ref()
const {
    data: currentRoleData,
    run: roleListApiRun,
    loading: roleListLoading,
    current: roleListCurrent,
    pageSize: roleListPageSize,
    totalPage: roleListTotalPage,
} = usePagination(roleApi.list, {
    defaultParams: [
        {
            pageSize: 999
        }
    ],
    onBefore: () => {
        startRolescoll.value = false
    },
    onSuccess: (data: IPageResponse<IRoleInfo>) => {
        if (data?.records) {
            roleData.value.push(...(data?.records))
        }
        startRolescoll.value = true
    }
});
const onSelectRole = (item: any) => {
    resourceTreeRun({
        "groupId": selectedGroupKeys.value[0],
        "roleId": item.key === 0 ? null : item.key,
        "parentId": 0,
    })
}

const visibleLinkRole = ref(false)
// 关联角色
const gotoLinkRole = () => {
    if (!selectedGroupKeys.value) {
        message.warn("请先选择一个组!")
        return
    }
    // 设置选中的内容
    roleSelectedRowKeys.value = []
    for (const item of  roleData.value) {
        if (item.id != 0) {
            roleSelectedRowKeys.value.push(item.id!)
        }
    }
    visibleLinkRole.value = true;
    linkRoleListRun({ pageNum: 1, pageSize: 10 })
}
const {
    data: linkRoleData,
    run: linkRoleListRun,
    loading: linkRoleLoading,
    current: linkRoleCurrent,
    pageSize: linkRolePageSize,
    totalPage: linkRoleTotalPage,
} = usePagination(roleApi.list);
const linkRolecColumns = [
    {
        title: '名称',
        dataIndex: 'name',
    },
    {
        title: '描述',
        dataIndex: 'description',
    },
];
const linkRolePagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: linkRoleData.value?.total,
    current: linkRoleData.value?.pageNum,
    pageSize: linkRoleData.value?.pageSize,
    style: { justifyContent: 'center' },
}));
const roleSelectedRowKeys = ref<Key[]>([])
const linkRoleSelection: TableProps['rowSelection'] = {
    preserveSelectedRowKeys: true,
    selectedRowKeys: roleSelectedRowKeys as any,
    onChange: (selectedRowKeys: Key[], selectedRows: DataType[]) => {
        roleSelectedRowKeys.value = selectedRowKeys
        console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
    getCheckboxProps: (record: DataType) => ({
        disabled: RoleSystemConstant.values("roleId").includes(record.id) , 
        name: record.name,
    }),
};

watch(roleSelectedRowKeys.value, (newValue) => {
    debugger
    console.log(newValue)
})

const {
    run: groupLinkRoleRun,
    loading: groupLinkRoleLoading,
} = useRequest(groupApi.linkRole, {
    onAfter: () => {
        message.success("成功");
        visibleLinkRole.value = false;
        handleGroupSearch({ current: 1, pageSize: pageSize.value })
    },
});

const linkRoleHandleOk = () => {
    groupLinkRoleRun({
        "groupId": selectedGroupKeys.value[0],
        "roleIds": roleSelectedRowKeys.value as any[]
    })
}


// 资源查询
const {
    data: resourceTreeData,
    run: resourceTreeRun,
    loading: resourceTreeLoading,
} = useRequest(
    resourceApi.searchTrees, {
}
);


const resourceTree = computed(() => {
    if (resourceTreeData.value) {
        return [
            {
                id: 0,
                name: 'ROOT',
                type: 0,
                children: [...resourceTreeData.value]
            }
        ]
    } else {
        return [
            {
                id: 0,
                name: 'ROOT',
                type: 0,
                children: []
            }
        ]
    }
});
const selectedTreeKeys = ref()

// 用户
const columns = [
    {
        title: '企业',
        dataIndex: 'enterprise',
        width: '10%',
    },
    {
        title: '最后登录时间',
        dataIndex: 'lastLoginTime',
        width: '10%',
    },
    {
        title: '账号',
        dataIndex: 'username',
        width: '10%',
    },
    {
        title: '手机号',
        dataIndex: 'phone',
        width: '10%',
    },
    {
        title: '昵称',
        dataIndex: 'nickName',
        width: '10%',
    },
    {
        title: '性别',
        dataIndex: '_genderName',
        width: '5%',
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: '5%',
    },
    {
        title: '邮箱',
        dataIndex: 'email',
        width: '10%',
    },
    {
        title: '创建时间',
        dataIndex: 'gmtCreate',
        width: '10%',
    },
    {
        title: '编辑',
        dataIndex: 'operator',
        width: '10%',
    },
];

const {
    data: userDataSource,
    run: userApiRun,
    loading,
    current: userListCurrent,
    pageSize: userListPageSize,
} = usePagination(userApi.list);
const userPagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: userDataSource.value?.total,
    current: userDataSource.value?.pageNum,
    pageSize: userDataSource.value?.pageSize,
    style: { justifyContent: 'center' },
}));



const handleUserHoverChange = () => {

}
const searchUserByParam = () => {

}
const userListParams = ref<IUserListRequest>({})
const handleUserSearch = (
    pag: { current: number; pageSize: number },
    filters?: any,
    sorter?: any,
) => {
    userApiRun({
        pageNum: pag.current,
        pageSize: pag.pageSize,
        ...userListParams.value
    });
};

// 关联用户
const visibleLinkUser = ref(false)
const gotoLinkUser = () => {
    if (!selectedGroupKeys.value) {
        message.warn("请先选择一个组!")
        return
    }
    visibleLinkUser.value = true;
    linkUserListRun({ pageNum: 1, pageSize: 10 })
}
const handleLinkUserSearch = (
    pag: { current: number; pageSize: number },
    filters?: any,
    sorter?: any,
) => {
    linkUserListRun({
        pageNum: pag.current,
        pageSize: pag.pageSize,
        ...linkUserListParams.value
    });
};
const linkUserListParams = ref<IUserListRequest>({})
const {
    data: linkUserData,
    run: linkUserListRun,
    loading: linkUserLoading,
    current: linkUserCurrent,
    pageSize: linkUserPageSize,
    totalPage: linkUserTotalPage,
} = usePagination(userApi.list, {
    pagination: {
        currentKey: 'pageNum',
        pageSizeKey: 'pageSize',
    }
});
const linkUserPagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: linkUserData.value?.total,
    current: linkUserData.value?.pageNum,
    pageSize: linkUserData.value?.pageSize,
    style: { justifyContent: 'center' },
}));
const linkUsercColumns = [
    {
        title: '企业',
        dataIndex: 'enterprise',
        width: '10%',
    },
    {
        title: '最后登录时间',
        dataIndex: 'lastLoginTime',
        width: '18%',
    },
    {
        title: '账号',
        dataIndex: 'username',
        width: '10%',
    },
    {
        title: '手机号',
        dataIndex: 'phone',
        width: '10%',
    },
    {
        title: '昵称',
        dataIndex: 'nickName',
        width: '10%',
    },
    {
        title: '性别',
        dataIndex: '_genderName',
        width: '5%',
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: '5%',
    },
    {
        title: '邮箱',
        dataIndex: 'email',
        width: '10%',
    },
    {
        title: '创建时间',
        dataIndex: 'gmtCreate',
        width: '18%',
    },
    {
        title: '操作',
        dataIndex: 'operator',
        width: '10%',
    },
];

const {
    run: groupLinkUserRun,
    loading: groupLinkUserLoading,
} = useRequest(groupApi.linkUser, {
    onSuccess: () => {
        message.success("成功");
        linkUserListRun({ pageNum: 1, pageSize: pageSize.value })
    },
});

const linkUserOne = (item: IUserInfo) => {
    if (selectedGroupKeys.value[0] && item.id && item.username) {
        groupLinkUserRun({
            "groupId": selectedGroupKeys.value[0],
            "userId": item.id,
            "username": item.username,
        })
    }
}
</script>

<template>
    <h-modal v-model:open="visibleLinkUser" :title="'关联用户'" :confirm-loading="groupLinkUserLoading"
        style="width: 1200px;" :footer="null">
        <h-row :align="'middle'">
            <h-col :span="23" style="margin-bottom: 10px;">
                <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
                    <h-col :span="2" style="text-align: right;padding-right: 10px;">
                        <label for="linkUserListParamsEnterprise">企业编码：</label>
                    </h-col>
                    <h-col :span="4">
                        <h-input id="linkUserListParamsEnterprise" v-model:value="linkUserListParams.enterpriseCode"
                            placeholder="" autocomplete="off" />
                    </h-col>
                    <h-col :span="2" style="text-align: right;padding-right: 10px;">
                        <label for="linkUserListParamsEnterprise">账号：</label>
                    </h-col>
                    <h-col :span="4">
                        <h-input id="linkUserListParamsUsername" v-model:value="linkUserListParams.username" placeholder=""
                            autocomplete="off" />
                    </h-col>
                    <h-col :span="2" style="text-align: right;padding-right: 10px;">
                        <label for="linkUserListParamsNickName">姓名：</label>
                    </h-col>
                    <h-col :span="4">
                        <h-input id="linkUserListParamsNickName" v-model:value="linkUserListParams.nickName" placeholder=""
                            autocomplete="off" />
                    </h-col>
                    <h-col :span="2" style="text-align: right;padding-right: 10px;">
                        <label for="linkUserListParamsState">状态：</label>
                    </h-col>
                    <h-col :span="4">
                        <h-input id="linkUserListParamsState" v-model:value="linkUserListParams.state" placeholder=""
                            autocomplete="off" />
                    </h-col>
                </h-row>
            </h-col>
            <h-col :span="1" style="margin-bottom: 10px;">
                <h-button type="primary" shape="circle" style="float: right;"
                    @click="handleLinkUserSearch({ current: 1, pageSize: 10 })">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                </h-button>
            </h-col>
            <h-col :span="24">
                <h-table :columns="linkUsercColumns" :row-key="record => record.id" size="small"
                    :data-source="linkUserData?.records" :pagination="linkUserPagination" :loading="linkUserLoading"
                    @change="handleLinkUserSearch($event as any)">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'enterprise' && record.enterpriseCode">
                            {{ record.enterpriseName }}({{ record.enterpriseCode }})
                        </template>
                        <template v-if="column.dataIndex === 'state'">
                            {{ record._stateName }}
                        </template>
                        <template v-if="column.dataIndex === 'operator'">
                            <h-button type="primary" shape="circle" style="float: right;" @click="linkUserOne(record)">
                                <template #icon>
                                    <PlusOutlined />
                                </template>
                            </h-button>
                        </template>
                    </template>
                </h-table>
            </h-col>
        </h-row>
    </h-modal>

    <h-modal v-model:open="visibleLinkRole" :title="'关联角色'" :confirm-loading="groupLinkRoleLoading"
        @ok="linkRoleHandleOk">
        <h-table :row-selection="linkRoleSelection" :columns="linkRolecColumns" :row-key="record => record.id" size="small"
            :data-source="linkRoleData?.records" :pagination="linkRolePagination" :loading="linkRoleLoading"
            @change="handleLinkRoleSearch($event as any)">
        </h-table>
    </h-modal>

    <h-modal v-model:open="visibleNewGroup" :title="'新增组'" :confirm-loading="groupSaveLoading" @ok="newGroupHandleOk">
        <h-form ref="groupNewForm" :model="newGroupFormModel" :label-col="{ span: 5 }" :wrapper-col="{ span: 14 }">
            <h-form-item label="名称" name="name" :rules="[{ required: true, message: '请输入组名!' }]">
                <h-input v-model:value="newGroupFormModel.name" />
            </h-form-item>
            <h-form-item label="描述" name="description">
                <h-input v-model:value="newGroupFormModel.description" />
            </h-form-item>
        </h-form>
    </h-modal>

    <h-row style="height: 100%;width: 100%;">
        <h-col :span="4" style="height:100% ; ">
            <div
                style="height:40px;border-right: 10px solid #f0f2f5;padding: 5px 5px 10px  20px  ;font-size: 18px;font-weight: 600;border-bottom: 1px solid #f0f0f0;background-color: #ffff;">
                组维护
                <h-popover title="名称检索" placement="bottom" trigger="click" :visible="visibleGroupSearch"
                    @visibleChange="handleGroupHoverChange">
                    <template #content>
                        <h-input-search v-model:value="groupSearchName" enter-button allow-clear @search="searchGroupByName"
                            autocomplete="off" />
                    </template>
                    <h-button type="primary" shape="circle" style="float: right;">
                        <template #icon>
                            <SearchOutlined />
                        </template>
                    </h-button>
                </h-popover>
                <h-button type="primary" shape="circle" style="float: right;margin-right: 3px;" @click="gotoNewGroup">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                </h-button>
            </div>
            <div style="height:calc(100% - 40px);border-right: 10px solid #f0f2f5;position: relative; ">
                <div v-if="groupListLoading"
                    style="float: left;position: absolute;height: 100%;width: 100%;text-align: center;padding-top: 100%;">
                    <h-spin />
                </div>
                <h-menu v-if="groupData" v-model:selectedKeys="selectedGroupKeys" @click="onSelectGroup"
                    style="height:100%;width: 100%;border: none;overflow-x: hidden;overflow-y: auto;" mode="inline"
                    ref="groupMenu">
                    <h-menu-item :key="i.id" v-for="i of groupData" @contextmenu.prevent.stop="groupMenuRightClick(i)">
                        <template v-if="UserGroupSystemConstant.values('groupId').includes(i.id)">
                            <div style="width: 100%;" :title="i.name">{{ i.name }}<span style="color: #d0d2d4;font-size: 10px;">/系统</span></div>
                        </template>
                        <template v-else>
                        <h-dropdown :trigger="['contextmenu']" placement="bottomLeft">
                            <div style="width: 100%;" :title="i.name">{{ i.name }}</div>
                            <template #overlay>
                                <h-menu @click="onContextGroupDeleteClick(i)">
                                    <h-menu-item>
                                        <DeleteOutlined />&nbsp;&nbsp;&nbsp;删除&nbsp;&nbsp;
                                    </h-menu-item>
                                </h-menu>
                            </template>
                        </h-dropdown>
                    </template>
                    </h-menu-item>
                </h-menu>
            </div>
        </h-col>
        <h-col :span="20" style="height:100% ; ">
            <h-row style="margin-bottom: 10px;height: calc(35% - 10px);width: 100%;">
                <h-col :span="4" style="height:100%;">
                    <div
                        style="height:40px;border-right: 10px solid #f0f2f5;padding: 5px 5px 10px  20px  ;font-size: 18px;font-weight: 600;border-bottom: 1px solid #f0f0f0;background-color: #ffff;">
                        组角色
                        <h-button type="primary" :disabled="!selectedGroupKeys" shape="circle"
                            style="float: right;margin-right: 3px;" @click="gotoLinkRole">
                            <template #icon>
                                <LinkOutlined />
                            </template>
                        </h-button>
                    </div>
                    <div style="height:calc(100% - 40px);border-right: 10px solid #f0f2f5;position: relative;">
                        <div v-if="roleListLoading"
                            style="float: left;position: absolute;height: 100%;width: 100%;text-align: center;padding-top: 100%;">
                            <h-spin />
                        </div>
                        <h-menu v-if="roleData" v-model:selectedKeys="selectedRoleKeys" @click="onSelectRole"
                            style="height:100%;width: 100%;border: none;overflow-x: hidden;overflow-y: auto;" mode="inline"
                            ref="roleMenu">
                            <h-menu-item :key="i.id" v-for="i of roleData" :title="i.name">
                                {{ i.name }}
                            </h-menu-item>
                        </h-menu>
                    </div>
                </h-col>
                <h-col :span="20" style="height: 100%;">
                    <div
                        style="height:40px;padding: 5px 20px 10px  20px  ;font-size: 18px;font-weight: 600;border-bottom: 1px solid #f0f0f0;background-color: #ffff;">
                            角色资源
                    </div>
                    <div
                        style="height:calc(100% - 40px); overflow-y: auto;padding: 5px 20px 10px  20px  ;background-color: #ffff;">
                        <h-tree v-if="resourceTree.length" :defaultExpandAll="true" :show-line="{ showLeafIcon: false }"
                            :show-icon="true" :tree-data="resourceTree as unknown as DataNode[]"
                            v-model:selectedKeys="selectedTreeKeys">
                            <template #icon="{ dataRef }">
                                <template v-if="dataRef.type === 0">
                                    <HomeOutlined />
                                </template>
                                <template v-if="dataRef.type === 1"><!-- 接口 -->
                                    <NodeExpandOutlined />
                                </template>
                                <template v-if="dataRef.type === 2"><!-- 页面 -->
                                    <FileOutlined />
                                </template>
                                <template v-if="dataRef.type === 3"><!-- 页面组 -->
                                    <FolderOutlined />
                                </template>
                                <template v-if="dataRef.type === 4"><!-- 组件 -->
                                    <UngroupOutlined />
                                </template>
                                <template v-if="dataRef.type === 5"><!-- 应用 -->
                                    <AppstoreOutlined />
                                </template>
                            </template>
                            <template #title="{ dataRef }">
                                <span>{{ dataRef.name }}</span>
                            </template>
                            <template #switcherIcon="{ switcherCls }">
                                <DownOutlined :class="switcherCls" />
                            </template>
                        </h-tree>
                    </div>
                </h-col>
            </h-row>
            <h-row style="height: 65%;width: 100%;overflow: auto;">
                <div
                    style="height:40px;width:100%;padding: 5px 5px 10px  20px  ;font-size: 18px;font-weight: 600;border-bottom: 1px solid #f0f0f0;background-color: #ffff;">
                    组用户
                    <template v-if="selectedGroupKeys">
                        <h-popover title="名称检索" placement="bottom" trigger="click" :visible="visibleRoleSearch"
                            @visibleChange="handleUserHoverChange">
                            <template #content>
                                <h-input-search v-model:value="roleSearchName" enter-button allow-clear
                                    @search="handleUserSearch({ current: 1, pageSize: 10 })" autocomplete="off" />
                            </template>
                            <h-button type="primary" shape="circle" style="float: right;">
                                <template #icon>
                                    <SearchOutlined />
                                </template>
                            </h-button>
                        </h-popover>
                    </template>
                    <template v-else>
                        <h-button type="primary" :disabled="true" shape="circle" style="float: right;">
                            <template #icon>
                                <SearchOutlined />
                            </template>
                        </h-button>
                    </template>
                    <h-button type="primary" :disabled="!selectedGroupKeys" shape="circle"
                        style="float: right;margin-right: 3px;" @click="gotoLinkUser">
                        <template #icon>
                            <LinkOutlined />
                        </template>
                    </h-button>
                </div>
                <div style="height:calc(100% - 40px);width:100%; overflow-y: auto;background-color: #ffff;">
                    <h-table :columns="columns" :row-key="record => record.id" size="small"
                        :data-source="userDataSource?.records" :pagination="userPagination" :loading="loading"
                        @change="handleUserSearch($event as any)">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'enterprise' && record.enterpriseCode">
                                {{ record.enterpriseName }}({{ record.enterpriseCode }})
                            </template>
                            <template v-if="column.dataIndex === 'state'">
                                {{ record._stateName }}
                            </template>
                            <template v-if="column.dataIndex === 'operator'">
                                修改
                            </template>
                        </template>
                    </h-table>
                </div>

            </h-row>
        </h-col>
    </h-row>
</template>

<style scoped lang="less">
.logo-div {
    text-align: center;
    width: 92%;

    .logo-title {
        line-height: 50px;
        color: rgb(255, 255, 255);
        font-size: 22px;
        font-weight: 600;
        text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
    }

    .logo-second-title {
        line-height: 0px;
        color: rgb(255, 255, 255);
        font-weight: 500;
        font-size: 4px;
        text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
    }
}

.trigger {
    font-size: 18px;
    line-height: 64px;
    padding: 0 24px;
    cursor: pointer;
    transition: color .3s;
}</style>
