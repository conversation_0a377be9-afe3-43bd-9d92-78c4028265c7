<template>
  <div
    style="
      background-color: #ffff;
      height: 100%;
      width: 100%;
      padding: 10px 10px 0px 10px;
      overflow: auto;
    "
  >
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <!-----------------查询表单----------------->
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px" :gutter="[0, 10]">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="regionId">区域：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              v-model:value="searchForm.regionId"
              style="width: 100%"
              v-bind="filterableSelectOption"
            >
              <h-select-option
                :value="item.id"
                v-for="item in options.area"
                :key="item.id"
                :label="item.regionNm"
              >
                {{ item.regionNm }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="status">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              v-model:value="searchForm.status"
              placeholder="请选择"
              style="width: 100%"
            >
              <h-select-option value="1">启用</h-select-option>
              <h-select-option value="0">停用</h-select-option>
            </h-select>
          </h-col>
        </h-row>

        <!-----------------列表按钮----------------->
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button type="primary" @click="queryHandle">
              <SearchOutlined /> 查询
            </h-button>
            <h-button @click="resetSearch" style="margin-left: 10px">
              <RedoOutlined /> 重置
            </h-button>
          </h-col>
        </h-row>

        <!-----------------功能按钮----------------->
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" class="btn-group">
            <h-button type="primary" @click="handleAdd()">
              <PlusOutlined /> 新增
            </h-button>
            <h-button type="primary" @click="handleEdit()">
              <EditOutlined /> 修改
            </h-button>
            <h-button type="primary" danger @click="deleteHandle">
              <DeleteOutlined />删除
            </h-button>
            <h-button @click="handleTableExport"> <DownloadOutlined /> 导出 </h-button>
          </h-col>
        </h-row>
      </h-col>

      <!-----------------列表----------------->
      <h-col :span="24">
        <list-table
          :api="api.list"
          :columns="columns"
          ref="listRef"
          :search-param="searchForm"
        >
          <template #bodyCell="{ column, record, index, text }">
            <template v-if="column.dataIndex == 'index'">
              {{ index + 1 }}
            </template>
            <template v-if="column.dataIndex == 'status'">
              <h-switch
                v-model:checked="record.status"
                checkedValue="1"
                unCheckedValue="0"
                size="small"
                @change="(e) => changeStatus(e, record)"
              />
            </template>
          </template>
          <!-- <template #expandedRowRender="{ record }">
            <table v-if="record.detailList">
              <thead>
                <tr>
                  <td>序号</td>
                  <td>产品类别</td>
                  <td>配送单价</td>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(detailItem, detailIndex) in record.detailList">
                  <td>{{ detailIndex + 1 }}</td>
                  <td>{{ detailItem.productName }}</td>
                  <td>{{ detailItem.price }}</td>
                </tr>
              </tbody>
            </table>
          </template>
          <template #expandColumnTitle>操作</template> -->
        </list-table>
      </h-col>
    </h-row>

    <!-- 新增弹窗 -->
    <h-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      @cancel="handleClose"
      @ok="handleSubmit"
      :ok-button-props="{ disabled: loading }"
      :cancel-button-props="{ disabled: loading }"
      :closable="false"
      width="600px"
    >
      <h-form
        ref="editFormRef"
        style="margin-top: 16px"
        :model="editForm"
        :label-col="{ style: { width: '120px' } }"
      >
        <h-form-item
          label="区域"
          name="regionId"
          :rules="[{ required: true, message: '请输入区域名称' }]"
        >
          <h-select
            v-model:value="editForm.regionId"
            style="width: 100%"
            v-bind="filterableSelectOption"
          >
            <h-select-option
              :value="item.id"
              v-for="item in options.area"
              :key="item.id"
              :label="item.regionNm"
            >
              {{ item.regionNm }}
            </h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item label="计费类型" name="costId" :rules="requiredItem">
          <h-select
            placeholder="请选择"
            v-model:value="editForm.costId"
            style="width: 100%"
          >
            <h-select-option
              :value="item.id"
              v-for="item in options.costType"
              :key="item.id"
              :label="item.costTypeNm"
              :disabled="item.id != 1 && item.id != 2"
            >
              {{ item.costTypeNm }}
            </h-select-option>
          </h-select>
        </h-form-item>
        <h-form-item
          label="包月费用"
          name="monthCost"
          :rules="requiredItem"
          v-if="editForm.costId == '1'"
        >
          <h-input placeholder="请输入" v-model:value="editForm.monthCost" />
        </h-form-item>

        <h-form-item label="状态" name="status" :rules="requiredItem">
          <h-select
            placeholder="请选择"
            v-model:value="editForm.status"
            style="width: 100%"
          >
            <h-select-option value="1">启用</h-select-option>
            <h-select-option value="0">停用</h-select-option>
          </h-select>
        </h-form-item>

        <h-form-item label="备注" name="remark">
          <h-textarea
            placeholder="请输入"
            v-model:value="editForm.remark"
            show-count
            :maxlength="100"
          />
        </h-form-item>
      </h-form>

      <!-- 弹窗明细 -->
      <template v-if="editForm.costId == '2'">
        <div class="btn-group">
          <h-button type="primary" @click="handleDetailAdd">
            <PlusOutlined /> 新增
          </h-button>
          <h-button type="primary" danger @click="handleDetailClear">
            <DeleteOutlined />删除
          </h-button>
        </div>

        <h-table :dataSource="editForm.detailList" :columns="detailColumns">
          <template #bodyCell="{ column, record, index, text }">
            <template v-if="column.dataIndex == 'index'">
              {{ index + 1 }}
            </template>
            <template v-if="column.dataIndex == 'productName'">
              <h-select
                v-model:value="record.productId"
                style="width: 100%"
                v-bind="filterableSelectOption"
              >
                <h-select-option
                  :value="item.id"
                  v-for="item in options.prod"
                  :key="item.id"
                  :label="item.productNm"
                >
                  {{ item.productNm }}
                </h-select-option>
              </h-select>
            </template>
            <template v-if="column.dataIndex == 'price'">
              <h-input placeholder="请输入" v-model:value="record.price" />
            </template>
            <template v-if="column.dataIndex == 'action'">
              <h-button type="link" @click="handleDetailDelete(index)">移除</h-button>
            </template>
          </template>
        </h-table>
      </template>
    </h-modal>
  </div>
</template>

<script setup lang="ts">
import {
  SearchOutlined,
  DownloadOutlined,
  PlusOutlined,
  RedoOutlined,
  DeleteOutlined,
  EditOutlined,
} from '@ant-design/icons-vue'
import {
  waterworkAreaLogicApi as api,
  waterworkAreaApi,
  waterworkCalcApi,
  waterworkProdApi,
} from '@haierbusiness-front/apis'
import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Form as hForm,
  FormItem as hFormItem,
  Modal as hModal,
  Textarea as hTextarea,
  message,
  Switch as hSwitch,
  Table as hTable,
} from 'ant-design-vue'
import { computed, ref, onMounted, nextTick } from 'vue'
import ListTable from '@/components/ListTable.vue'
import { filterableSelectOption } from '@/utils/select'

const sorter = (a: any, b: any) => a.applicationForm.localeCompare(b.applicationForm)
const loading = ref(false)

const listRef = ref<any>(null)
const selection = computed(() => listRef.value?.selection)
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
  },
  {
    title: '区域名称',
    dataIndex: 'regionName',
    sorter: (a: any, b: any) => sorter(a, b),
  },
  {
    title: '计费类型',
    dataIndex: 'costName',
    sorter: (a: any, b: any) => sorter(a, b),
  },
  {
    title: '添加人',
    dataIndex: 'createName',
    sorter: (a: any, b: any) => sorter(a, b),
  },
  {
    title: '添加时间',
    dataIndex: 'gmtCreate',
    sorter: (a: any, b: any) => sorter(a, b),
  },
  {
    title: '状态',
    dataIndex: 'status',
    sorter: (a: any, b: any) => sorter(a, b),
  },
  {
    title: '备注',
    dataIndex: 'remark',
    sorter: (a: any, b: any) => sorter(a, b),
  },
]

type SearchFormDef = {
  regionId: string
  status: string
}

const searchForm = ref<SearchFormDef>({
  regionId: '',
  status: '',
})

type EditFormDef = {
  regionId: string
  costId: string
  monthCost: string
  status: string
  remark: string
  detailList: Array<any>
}
const editForm = ref<EditFormDef>({
  regionId: '',
  costId: '',
  monthCost: '',
  status: '1',
  remark: '',
  detailList: [],
})
const requiredItem = { required: true, message: '该项为必填' }
const modalVisible = ref(false)
const modalTitle = ref('') // 弹窗标题
const editFormRef = ref<any>(null)
const options = ref<any>({
  prod: [],
  area: [],
  costType: [],
})
const detailColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
  },
  {
    title: '产品类别',
    dataIndex: 'productName',
  },
  {
    title: '配送单价',
    dataIndex: 'price',
  },
  {
    title: '操作',
    dataIndex: 'action',
  },
]

// 初始化
onMounted(async () => {
  getAreaOption()
  getCostTypeOption()
  getProdOption()
})

// -------------------下拉框初始化 start---------------------
const getAreaOption = async () => {
  let res = await waterworkAreaApi.listAll({ status: 1 })
  // console.log(res, '区域列表')
  options.value.area = res
}
const getCostTypeOption = async () => {
  let res = await waterworkCalcApi.listAll({ status: 1 })
  // console.log(res, '计费类型列表')
  options.value.costType = res
}
const getProdOption = async () => {
  let res = await waterworkProdApi.listAll({ status: 1 })
  // console.log(res, '产品列表')
  options.value.prod = res
}
// -------------------下拉框初始化 end---------------------

// -------------------列表操作 start---------------------
const queryHandle = async () => {
  listRef.value.resetPageNum()
}
const getList = async () => {
  await listRef.value.getList()
}

const resetSearch = async () => {
  searchForm.value = {
    regionId: '',
    status: '',
  }
  await nextTick()
  await getList()
}

const handleTableExport = async () => {
  await api.export({ ...searchForm.value })
}

const deleteHandle = async () => {
  console.log(selection.value, '选中项')
  if (!selection.value.length) {
    return message.warning('请选择至少一条数据')
  }
  await new Promise((resolve, reject) => {
    hModal.confirm({
      title: '提示',
      content: '确定要删除选定的数据吗?',
      onOk() {
        resolve(null)
      },
      onCancel() {
        reject()
      },
    })
  })
  try {
    await api.delete(selection.value.map((e: any) => e.id).join(','))
    message.success('操作成功')
    await getList()
  } finally {
    loading.value = false
  }
}

const changeStatus = async (checked: any, record: any) => {
  console.log(checked, '更改')
  loading.value = true
  try {
    await api.update({
      id: record.id,
      status: checked,
    })
    message.success('操作成功')
    await getList()
  } finally {
    loading.value = false
  }
}
// -------------------列表操作 end---------------------

// -------------------新增编辑表单 start---------------------
const handleAdd = async () => {
  modalTitle.value = '新增'
  modalVisible.value = true
}
const handleEdit = async () => {
  if (selection.value.length != 1) {
    return message.warning('请选择一条数据')
  }
  try {
    loading.value = true
    modalTitle.value = '修改'
    let row = selection.value[0]
    let res = await api.detail(row.id)
    // console.log(res, 666)
    editForm.value = {
      ...row,
      detailList: res?.detailList?.map((e: any) => {
        return {
          ...e,
          productId: Number(e.productId),
        }
      }),
    }
    modalVisible.value = true
    // console.log(editForm.value, row, 999)
  } finally {
    loading.value = false
  }
}
const handleClose = () => {
  editForm.value = {
    regionId: '',
    costId: '',
    monthCost: '',
    status: '1',
    remark: '',
    detailList: [],
  }
  modalVisible.value = false
}
const handleSubmit = async () => {
  await editFormRef.value.validateFields()
  let { monthCost, costId, ...otherParam } = editForm.value
  let param = { monthCost: '', costId, ...otherParam }
  // 月结类型传包月费用 按件传明细
  if (costId == '1') {
    param.monthCost = monthCost
  } else {
    param.detailList = editForm.value.detailList
    if (!param.detailList.length) {
      return message.warning('明细不得为空')
    }
  }
  loading.value = true
  try {
    if (modalTitle.value == '新增') {
      await api.add(param)
    } else {
      // console.log(selection.value[0], 888)
      await api.update({
        id: selection.value[0].id,
        ...param,
      })
    }
    handleClose()
    message.success('操作成功')
    await getList()
  } finally {
    loading.value = false
  }
}
// -------------------新增编辑表单 end---------------------

// -------------------明细表操作 start---------------------
const handleDetailAdd = () => {
  editForm.value.detailList.unshift({})
}
const handleDetailClear = () => {
  editForm.value.detailList = []
}
const handleDetailDelete = (index: number) => {
  editForm.value.detailList.splice(index, 1)
}
// -------------------明细表操作 end---------------------
</script>

<style scoped lang="less">
.btn-group {
  text-align: left;
  button {
    margin-right: 8px;
  }
}
</style>
