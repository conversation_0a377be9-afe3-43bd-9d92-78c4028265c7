<template>
  <div
    style="
      background-color: #ffff;
      height: 100%;
      width: 100%;
      padding: 10px 10px 0px 10px;
      overflow: auto;
    "
  >
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <!-----------------查询表单----------------->
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px" :gutter="[0, 10]">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="costTypeNm">类型：</label>
          </h-col>
          <h-col :span="4">
            <h-input
              id="costTypeNm"
              v-model:value="searchForm.costTypeNm"
              placeholder="请输入"
              autocomplete="off"
            />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="status">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              v-model:value="searchForm.status"
              placeholder="请选择"
              style="width: 100%"
            >
              <h-select-option value="1">启用</h-select-option>
              <h-select-option value="0">停用</h-select-option>
            </h-select>
          </h-col>
        </h-row>

        <!-----------------列表按钮----------------->
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button type="primary" @click="queryHandle">
              <SearchOutlined /> 查询
            </h-button>
            <h-button @click="resetSearch" style="margin-left: 10px">
              <RedoOutlined /> 重置
            </h-button>
          </h-col>
        </h-row>

        <!-----------------功能按钮----------------->
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" class="btn-group">
            <h-button type="primary" @click="handleAdd()">
              <PlusOutlined /> 新增
            </h-button>
            <h-button type="primary" @click="handleEdit()">
              <EditOutlined /> 修改
            </h-button>
            <h-button type="primary" danger @click="deleteHandle">
              <DeleteOutlined />删除
            </h-button>
            <h-button @click="handleTableExport"> <DownloadOutlined /> 导出 </h-button>
          </h-col>
        </h-row>
      </h-col>

      <!-----------------列表----------------->
      <h-col :span="24">
        <list-table
          :api="api.list"
          :columns="columns"
          ref="listRef"
          :search-param="searchForm"
        >
          <template #bodyCell="{ column, record, index, text }">
            <template v-if="column.dataIndex == 'index'">
              {{ index + 1 }}
            </template>
            <template v-if="column.dataIndex == 'status'">
              <h-switch
                v-model:checked="record.status"
                checkedValue="1"
                unCheckedValue="0"
                size="small"
                @change="(e) => changeStatus(e, record)"
              />
            </template>
          </template>
        </list-table>
      </h-col>
    </h-row>

    <!-- 新增弹窗 -->
    <h-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @cancel="handleClose"
      @ok="handleSubmit"
      :ok-button-props="{ disabled: loading }"
      :cancel-button-props="{ disabled: loading }"
      :closable="false"
    >
      <h-form
        ref="editFormRef"
        style="margin-top: 16px"
        :model="editForm"
        :label-col="{ style: { width: '120px' } }"
      >
        <h-form-item label="类型" name="costTypeNm" :rules="requiredItem">
          <h-input placeholder="请输入" v-model:value="editForm.costTypeNm" />
        </h-form-item>
        <h-form-item label="状态" name="status" :rules="requiredItem">
          <h-select
            placeholder="请选择"
            v-model:value="editForm.status"
            style="width: 100%"
          >
            <h-select-option value="1">启用</h-select-option>
            <h-select-option value="0">停用</h-select-option>
          </h-select>
        </h-form-item>

        <h-form-item label="备注" name="remark">
          <h-textarea
            placeholder="请输入"
            v-model:value="editForm.remark"
            show-count
            :maxlength="100"
          />
        </h-form-item>
      </h-form>
    </h-modal>
  </div>
</template>

<script setup lang="ts">
import {
  SearchOutlined,
  DownloadOutlined,
  PlusOutlined,
  RedoOutlined,
  DeleteOutlined,
  EditOutlined,
} from '@ant-design/icons-vue'
import { waterworkCalcApi as api } from '@haierbusiness-front/apis'
import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Form as hForm,
  FormItem as hFormItem,
  Modal as hModal,
  Textarea as hTextarea,
  message,
  Switch as hSwitch,
} from 'ant-design-vue'
import { computed, ref, onMounted, nextTick } from 'vue'
import ListTable from '@/components/ListTable.vue'

// 初始化
onMounted(async () => {})

const sorter = (a: any, b: any) => a.applicationForm.localeCompare(b.applicationForm)
const loading = ref(false)

const listRef = ref<any>(null)
const selection = computed(() => listRef.value?.selection)
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
  },
  {
    title: '类型',
    dataIndex: 'costTypeNm',
    sorter: (a: any, b: any) => sorter(a, b),
  },
  {
    title: '添加人',
    dataIndex: 'createName',
    sorter: (a: any, b: any) => sorter(a, b),
  },
  {
    title: '添加时间',
    dataIndex: 'gmtCreate',
    sorter: (a: any, b: any) => sorter(a, b),
  },
  {
    title: '状态',
    dataIndex: 'status',
    sorter: (a: any, b: any) => sorter(a, b),
  },
  {
    title: '备注',
    dataIndex: 'remark',
    sorter: (a: any, b: any) => sorter(a, b),
  },
]

type SearchFormDef = {
  costTypeNm: string
  status: string
}

const searchForm = ref<SearchFormDef>({
  costTypeNm: '',
  status: '',
})

type EditFormDef = {
  costTypeNm: string
  status: string
  remark: string
}
const editForm = ref<EditFormDef>({
  costTypeNm: '',
  status: '1',
  remark: '',
})
const requiredItem = { required: true, message: '该项为必填' }
const modalVisible = ref(false)
const modalTitle = ref('') // 弹窗标题
const editFormRef = ref<any>(null)

// -------------------列表操作 start---------------------
const queryHandle = async () => {
  listRef.value.resetPageNum()
}
const getList = async () => {
  await listRef.value.getList()
}

const resetSearch = async () => {
  searchForm.value = {
    costTypeNm: '',
    status: '',
  }
  await nextTick()
  await getList()
}

const handleTableExport = async () => {
  await api.export({ ...searchForm.value })
}

const deleteHandle = async () => {
  console.log(selection.value, '选中项')
  if (!selection.value.length) {
    return message.warning('请选择至少一条数据')
  }
  await new Promise((resolve, reject) => {
    hModal.confirm({
      title: '提示',
      content: '确定要删除选定的数据吗?',
      onOk() {
        resolve(null)
      },
      onCancel() {
        reject()
      },
    })
  })
  try {
    await api.delete(selection.value.map((e: any) => e.id).join(','))
    message.success('操作成功')
    await getList()
  } finally {
    loading.value = false
  }
}

const changeStatus = async (checked: any, record: any) => {
  console.log(checked, '更改')
  loading.value = true
  try {
    await api.update({
      id: record.id,
      status: checked,
    })
    message.success('操作成功')
    await getList()
  } finally {
    loading.value = false
  }
}
// -------------------列表操作 end---------------------

// -------------------新增编辑表单 start---------------------
const handleAdd = async () => {
  modalTitle.value = '新增'
  modalVisible.value = true
}
const handleEdit = async () => {
  if (selection.value.length != 1) {
    return message.warning('请选择一条数据')
  }
  modalTitle.value = '修改'
  let row = selection.value[0]
  editForm.value = {
    ...row,
  }
  modalVisible.value = true
  // console.log(editForm.value, row, 999)
}
const handleClose = () => {
  editFormRef.value.resetFields()
  modalVisible.value = false
}
const handleSubmit = async () => {
  let param = await editFormRef.value.validateFields()
  loading.value = true
  try {
    if (modalTitle.value == '新增') {
      await api.add({ ...param })
    } else {
      // console.log(selection.value[0], 888)
      await api.update({
        id: selection.value[0].id,
        ...param,
      })
    }
    handleClose()
    message.success('操作成功')
    await getList()
  } finally {
    loading.value = false
  }
}
// -------------------新增编辑表单 end---------------------
</script>

<style scoped lang="less">
.btn-group {
  text-align: left;
  button {
    margin-right: 8px;
  }
}
</style>
