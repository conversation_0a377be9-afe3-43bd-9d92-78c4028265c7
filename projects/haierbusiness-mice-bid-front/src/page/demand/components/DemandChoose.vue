<script setup lang="ts">
// 需求模块选择

import { onMounted, ref, reactive, defineProps, defineEmits, defineExpose } from 'vue';

const props = defineProps({
  cacheStr: {
    type: String,
    default: '',
  },
  demandSets: {
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['demandChooseFunc']);

const state = reactive({
  indeterminate: false,
  checkAll: false,
  checkedList: [],
  defaultList: ['plan'],
});

// checkBox组
const planOptions = ref<Array>([]);
const defaultOptions = [{ label: '日程安排', value: 'plan', disabled: true }];

// 全选
const onCheckAllChange = (e: any) => {
  Object.assign(state, {
    checkedList: e.target.checked
      ? planOptions.value.map((e) => {
          return e.value;
        })
      : [],
    indeterminate: false,
  });

  emit('demandChooseFunc', [...state.checkedList]);
};

const onChange = (param) => {
  state.indeterminate = !!param.length && param.length < planOptions.value.length;
  state.checkAll = param.length === planOptions.value.length;

  emit('demandChooseFunc', [...state.checkedList]);
};

// 暂存
const tempSave = () => {
  emit('demandChooseFunc', [...state.checkedList]);
};

defineExpose({ tempSave });

onMounted(async () => {
  // 流程编排
  planOptions.value = [];
  if (props.demandSets.includes(128)) {
    planOptions.value.push({ label: '布展物料', code: 128, value: 'material', disabled: false });
  }
  // TODO
  // if (props.demandSets.includes(256)) {
  //   planOptions.value.push({ label: '票务预定', code: 256, value: 'ticket', disabled: false });
  // }
  if (props.demandSets.includes(512)) {
    planOptions.value.push({ label: '礼品需求', code: 512, value: 'gift', disabled: false });
  }
  if (props.demandSets.includes(1024)) {
    planOptions.value.push({ label: '其他需求', code: 1024, value: 'other', disabled: false });
  }

  // 反显
  if (props.cacheStr) {
    const cacheObj = JSON.parse(props.cacheStr);

    // 布展物料-material
    // 票务预定-ticket
    // 礼品需求-gift
    // 其他需求-other
    if (cacheObj.material && Object.keys(cacheObj.material).length > 0) {
      state.checkedList.push('material');
    }

    if (cacheObj.traffic && Object.keys(cacheObj.traffic).length > 0) {
      state.checkedList.push('ticket');
    }
    if (cacheObj.presents && Object.keys(cacheObj.presents).length > 0) {
      state.checkedList.push('gift');
    }
    if (cacheObj.others && Object.keys(cacheObj.others).length > 0) {
      state.checkedList.push('other');
    }

    onChange(state.checkedList);
  }
});
</script>

<template>
  <!-- 需求模块选择 -->
  <div class="demand_choose demand_pad24">
    <div class="demand_choose_title">
      <div class="demand_choose_title_img"></div>
      请选择您要提报的需求模块：
    </div>
    <div class="demand_choose_checked mt16">
      <a-checkbox
        class="mr8"
        v-model:checked="state.checkAll"
        :indeterminate="state.indeterminate"
        @change="onCheckAllChange"
      >
        <span class="demand_choose_all">全选</span>
      </a-checkbox>

      <a-checkbox-group v-model:value="state.defaultList" :options="defaultOptions" />
      <a-checkbox-group v-model:value="state.checkedList" :options="planOptions" @change="onChange" />
    </div>
  </div>
</template>

<style scoped lang="less">
.demand_choose {
  border-bottom: 1px solid #f1f2f6;

  .demand_choose_title {
    font-weight: 500;
    color: #1d2129;
    line-height: 20px;

    position: relative;

    .demand_choose_title_img {
      position: absolute;
      left: -12px;
      top: -3px;

      width: 7px;
      height: 24px;
      background: url('@/assets/image/demand/exclamationMark.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
  .demand_choose_checked {
    /* 多选框 */
    :deep(:where(.css-dev-only-do-not-override-bq26c2).ant-checkbox + span) {
      padding-inline-end: 24px;
    }

    .demand_choose_all {
      color: #1868db;
    }
  }
}
</style>
