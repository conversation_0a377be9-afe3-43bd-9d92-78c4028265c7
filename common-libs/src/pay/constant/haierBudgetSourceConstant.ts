

type keys = 'BCC' | 'GEMS' | 'KEMS' | 'RRSGEMS' | 'HBC' |'CZY'|'XW';

/**
 * 预算类型
 */
export const HaierBudgetSourceConstant = {
  BCC: { "code": "BCC", "name": "BC<PERSON>" },
  GEMS: { "code": "GEMS", "name": "GEMS" },
  KEMS: { "code": "KEMS", "name": "KEMS" },
  RRSGEMS: { "code": "RRSGE<PERSON>", "name": "R<PERSON><PERSON><PERSON>" },
  HBC: { "code": "HBC", "name": "HBC" },
  HSH: { "code": "HSH", "name": "HSH" },
  SCAN_CODE: { "code": "SCAN_CODE", "name": "SCAN_CODE" },
  XW: { "code": "XWFIN", "name": "XWFIN" },
  CZY: { "code": "CZY", "name": "<PERSON>Z<PERSON>" },
  ofCode: (code?: string): { "code": string,"name": string } | null => {
    for (const key in HaierBudgetSourceConstant) {
      const item = HaierBudgetSourceConstant[key as keys];
      if (code === item.code) {
        return item;
      }
    }
    return null;
  }
}