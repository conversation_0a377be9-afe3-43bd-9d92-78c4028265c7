// 会议服务商类型

type keys = 'UNDER_CHANGE' | 'CHANGE_REJECTION' | 'CHANGE_COMPLETED'

export const SchemeChangeState = {
  // ZERO: { code: 0, desc: '/', disabled:true, },
  UNDER_CHANGE: { code: 1, desc: '变更中' },
  CHANGE_REJECTION: { code: 2, desc: '变更驳回' },
  CHANGE_COMPLETED: { code: 3, desc: '变更完成' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in SchemeChangeState) {
      const item = SchemeChangeState[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(SchemeChangeState).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return SchemeChangeState[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
