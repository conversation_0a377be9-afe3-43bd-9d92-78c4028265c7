// 付款单状态枚举
export enum PaymentFromStatusEnum {
    PENDING_PAYMENT_UPLOAD = 10,      // 待上传支付凭证
    PENDING_FINANCIAL_CONFIRM = 20,  // 待财务确认收款
    PAYMENT_REJECTED = 30,           // 收款驳回
    PENDING_INVOICE_UPLOAD = 40,     // 待国旅上传发票
    APPROVAL_REJECTED = 50,           // 审批驳回
    APPROVING = 60,                  // 审批中
    COMPLETED = 70,                  // 已完成
  }

// 发票状态枚举
export enum InvoiceStatusEnum {
    PENDING_SERVICE_PROVIDER_UPLOAD = 10,  // 待服务商上传发票
    PENDING_FINANCIAL_CONFIRM = 20,        // 待财务确认收款
    FINANCIAL_REJECTED = 30,               // 财务驳回
    COMPLETED = 40,                        // 已完成
  }
  
  export const PaymentFromStatusMap = {
    [PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD]: '待上传支付凭证',
    [PaymentFromStatusEnum.PENDING_FINANCIAL_CONFIRM]: '待财务确认收款',
    [PaymentFromStatusEnum.PAYMENT_REJECTED]: '收款驳回',
    [PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD]: '待国旅上传发票',
    [PaymentFromStatusEnum.APPROVAL_REJECTED]: '审批驳回',
    [PaymentFromStatusEnum.APPROVING]: '审批中',
    [PaymentFromStatusEnum.COMPLETED]: '已完成',
  } as const;

export const InvoiceStatusMap = {
    [InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD]: '待服务商上传发票',
    [InvoiceStatusEnum.PENDING_FINANCIAL_CONFIRM]: '待财务确认收款',
    [InvoiceStatusEnum.FINANCIAL_REJECTED]: '财务驳回',
    [InvoiceStatusEnum.COMPLETED]: '已完成',
  } as const;

export const PaymentFromStatusTagColorMap = {
  [PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD]: 'orange',
  [PaymentFromStatusEnum.PENDING_FINANCIAL_CONFIRM]: 'blue',
  [PaymentFromStatusEnum.PAYMENT_REJECTED]: 'red',
  [PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD]: 'purple',
  [PaymentFromStatusEnum.APPROVAL_REJECTED]: 'red',
  [PaymentFromStatusEnum.APPROVING]: 'processing',
  [PaymentFromStatusEnum.COMPLETED]: 'green',
} as const;

export const InvoiceStatusTagColorMap = {
  [InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD]: 'orange',
  [InvoiceStatusEnum.PENDING_FINANCIAL_CONFIRM]: 'blue',
  [InvoiceStatusEnum.FINANCIAL_REJECTED]: 'red',
  [InvoiceStatusEnum.COMPLETED]: 'green',
} as const;

// 确认状态枚举
export enum ConfirmStatusEnum {
  PENDING = 0,    // 待确认
  CONFIRMED = 1,  // 已确认
  REJECTED = 2,   // 驳回
}

export const ConfirmStatusMap = {
  [ConfirmStatusEnum.PENDING]: '待确认',
  [ConfirmStatusEnum.CONFIRMED]: '通过',
  [ConfirmStatusEnum.REJECTED]: '驳回',
} as const;

// 文件类型枚举
export enum FileTypeEnum {
  REFUND_VOUCHER_FINANCE = 160,           // 退款凭证（财务上传）
  PAYMENT_VOUCHER_FINANCE = 161,          // 支付凭证（财务上传）
  PAYMENT_VOUCHER_SERVICE_PROVIDER = 162,  // 支付凭证（服务商上传）
  RECEIPT_VOUCHER_FINANCE = 163,          // 收款凭证（财务上传）
  PAYMENT_VOUCHER_SERVICE_PROVIDER_2 = 164, // 支付凭证（服务商上传）
  PAYMENT_VOUCHER_FINANCE_2 = 165,        // 付款凭证（财务上传）
  ACCOMMODATION_DETAIL_ATTACHMENT = 560,   // 住宿详单附件
  EXCHANGE_RATE_EVIDENCE = 570,           // 汇率见证性材料
  INSURANCE_ATTACHMENT = 580,              // 保单附件
  SETTLEMENT_ATTACHMENT = 590,             // 结算单附件
}

// 文件类型映射
export const FileTypeMap = {
  [FileTypeEnum.REFUND_VOUCHER_FINANCE]: '退款凭证（财务上传）',
  [FileTypeEnum.PAYMENT_VOUCHER_FINANCE]: '支付凭证（财务上传）',
  [FileTypeEnum.PAYMENT_VOUCHER_SERVICE_PROVIDER]: '支付凭证（服务商上传）',
  [FileTypeEnum.RECEIPT_VOUCHER_FINANCE]: '收款凭证（财务上传）',
  [FileTypeEnum.PAYMENT_VOUCHER_SERVICE_PROVIDER_2]: '支付凭证（服务商上传）',
  [FileTypeEnum.PAYMENT_VOUCHER_FINANCE_2]: '付款凭证（财务上传）',
  [FileTypeEnum.ACCOMMODATION_DETAIL_ATTACHMENT]: '住宿详单附件',
  [FileTypeEnum.EXCHANGE_RATE_EVIDENCE]: '汇率见证性材料',
  [FileTypeEnum.INSURANCE_ATTACHMENT]: '保单附件',
  [FileTypeEnum.SETTLEMENT_ATTACHMENT]: '结算单附件',
} as const;

// 系统模块枚举
export enum SystemModuleEnum {
  SYSTEM = 'haierbusiness-system',
  AUTH = 'haierbusiness-auth',
  PAY = 'haierbusiness-pay',
  BALANCE = 'haierbusiness-balance',
  PROCESS = 'haierbusiness-process',
  COMMON = 'haierbusiness-common',
  WYY = 'haierbusiness-wyy',
  JD = 'haierbusiness-jd',
  DSCS = 'haierbusiness-dscs',
  DATA = 'haierbusiness-data',
  ENTERPRISE = 'haierbusiness-enterprise',
  PORTAL = 'haierbusiness-portal',
  DAILY = 'haierbusiness-daily',
  PORTAL_INDEX = 'haierbusiness-portalIndex',
  PROCESSMAN = 'haierbusiness-processman',
  MICE = 'haierbusiness-mice',
  TRAVEL_COST = 'travelbusiness-cost',
  HAIER_MICE = 'haiermice',
  WALLET = 'haierbusiness-wallet',
  HOTEL_SUPPORT = 'haierbusiness-hotel-support',
  RESOURCE_FRONT = 'haierbusiness-resource-front',
  RESOURCE = 'haierbusiness-resource',
  HOTEL_ANALYSIS = 'haierbusiness-hotel-analysis',
  MERCHANT = 'haierbusiness-merchant',
  BUSINESS_TRIP = 'haierbusiness-business-trip',
  CUSTOMER_SERVICE = 'haierbusiness-customer-service',
  BUSINESS_TRIP_MANAGE = 'haierbusiness-business-trip-manage',
  BUSINESS_TEAM = 'haierbusiness-business-team',
  TEAM_ORDER = 'haierbusiness-team-order',
  LOCAL_HOTEL = 'haierbusiness-localhotel',
  INCENTIVE = 'haierbusiness-incentive',
  HOTEL = 'haierbusiness-hotel',
  HOTEL_MAPPING = 'haierbusiness-hotel-mapping',
  BANQUET = 'haierbusiness-banquet',
  CAR_RENTAL = 'haierbusiness-carrental',
  HELPER = 'haierbusiness-helper',
  MICE_BID = 'haierbusiness-mice-bid',
  BUSINESS_HELPER_SERVICE = 'haierbusiness-business-helper-service',
  TRAVEL_SERVICE = 'haierbusiness-travelservice',
  MICE_BIDMAN = 'haierbusiness-mice-bidman',
  MICE_MERCHANT = 'haierbusiness-mice-merchant',
  KIMMY_TEST = 'kimmy-test',
  WATERWORKS = 'haierbusiness-waterworks',
  WATERWORKSMAN_FRONT = 'haierbusiness-waterworksman-front',
  WATERWORKS_FRONT = 'haierbusiness-waterworks-front',
  AI = 'haierbusiness-ai',
  PARKADE = 'haierbusiness-parkade',
  MICE_SERVICE = 'haierbusiness-mice-service',
  MICE_SUPPORT = 'haierbusiness-mice-support',
  MICE_SUPPORTMAN = 'haierbusiness-mice-supportman',
  FINANCEMAN = 'haierbusiness-financeman',
}

// 系统模块映射
export const SystemModuleMap = {
  [SystemModuleEnum.SYSTEM]: '系统管理',
  [SystemModuleEnum.AUTH]: '认证中心',
  [SystemModuleEnum.PAY]: '支付中心',
  [SystemModuleEnum.BALANCE]: '结算中心',
  [SystemModuleEnum.PROCESS]: '审批中心',
  [SystemModuleEnum.COMMON]: '通用行为中心',
  [SystemModuleEnum.WYY]: '网易云超市',
  [SystemModuleEnum.JD]: '京东云超市',
  [SystemModuleEnum.DSCS]: '电商超市',
  [SystemModuleEnum.DATA]: '数据中台',
  [SystemModuleEnum.ENTERPRISE]: '企业中心',
  [SystemModuleEnum.PORTAL]: '门户',
  [SystemModuleEnum.DAILY]: '商务日清系统',
  [SystemModuleEnum.PORTAL_INDEX]: '首页',
  [SystemModuleEnum.PROCESSMAN]: '审批后台页面',
  [SystemModuleEnum.MICE]: '商务会展(新)',
  [SystemModuleEnum.TRAVEL_COST]: '差旅费控系统',
  [SystemModuleEnum.HAIER_MICE]: '商务会展',
  [SystemModuleEnum.WALLET]: '移动端支付',
  [SystemModuleEnum.HOTEL_SUPPORT]: '酒店支撑模块',
  [SystemModuleEnum.RESOURCE_FRONT]: '资源中台',
  [SystemModuleEnum.RESOURCE]: '资源中台',
  [SystemModuleEnum.HOTEL_ANALYSIS]: '酒店分析聚合系统',
  [SystemModuleEnum.MERCHANT]: '商户中心',
  [SystemModuleEnum.BUSINESS_TRIP]: '商务云出差申请单',
  [SystemModuleEnum.CUSTOMER_SERVICE]: '话务中心',
  [SystemModuleEnum.BUSINESS_TRIP_MANAGE]: '商旅管理',
  [SystemModuleEnum.BUSINESS_TEAM]: '团队票',
  [SystemModuleEnum.TEAM_ORDER]: '团队票',
  [SystemModuleEnum.LOCAL_HOTEL]: '青岛餐房',
  [SystemModuleEnum.INCENTIVE]: '工会达产激励',
  [SystemModuleEnum.HOTEL]: '酒店',
  [SystemModuleEnum.HOTEL_MAPPING]: '酒店mapping',
  [SystemModuleEnum.BANQUET]: '异地宴请',
  [SystemModuleEnum.CAR_RENTAL]: 'ehai',
  [SystemModuleEnum.HELPER]: '创客帮',
  [SystemModuleEnum.MICE_BID]: '会务招标系统',
  [SystemModuleEnum.BUSINESS_HELPER_SERVICE]: '创客帮互助',
  [SystemModuleEnum.TRAVEL_SERVICE]: '商旅出差申请单',
  [SystemModuleEnum.MICE_BIDMAN]: '会务招标后台',
  [SystemModuleEnum.MICE_MERCHANT]: '会展商户端',
  [SystemModuleEnum.KIMMY_TEST]: '测试2',
  [SystemModuleEnum.WATERWORKS]: '饮用水厂后端',
  [SystemModuleEnum.WATERWORKSMAN_FRONT]: '引用水厂后端管理',
  [SystemModuleEnum.WATERWORKS_FRONT]: '引用水厂前端',
  [SystemModuleEnum.AI]: '商务AI中台',
  [SystemModuleEnum.PARKADE]: '停车权益券',
  [SystemModuleEnum.MICE_SERVICE]: '会中系统',
  [SystemModuleEnum.MICE_SUPPORT]: '会中业务系统',
  [SystemModuleEnum.MICE_SUPPORTMAN]: '会中后台系统',
  [SystemModuleEnum.FINANCEMAN]: '财务系统',
} as const;
  
    