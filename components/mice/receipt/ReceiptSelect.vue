<!-- 收款记录选择组件 -->

<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Input as hInput,
  Table as hTable,
  RangePicker as hRangePicker,
  message,
  Modal,
} from 'ant-design-vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { bondApi } from '@haierbusiness-front/apis';
import { ReceiptRecord, ReceiptSearchParam } from '@haierbusiness-front/common-libs';
import { computed, ref, watch } from 'vue';

// 日期格式化函数
const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

type EmitType = {
  (e: 'update:visible', visible: boolean): void;
  (e: 'select', record: ReceiptRecord): void;
  (e: 'cancel'): void;
};

const props = defineProps<{
  visible: boolean;
}>();

const emit = defineEmits<EmitType>();

// 收款记录搜索参数
const receiptSearchParam = ref<ReceiptSearchParam>({
  merchantName: '',
  payTimeStart: null,
  payTimeEnd: null,
  code: [] as string[]
});
const receiptTimeRange = ref<[Date, Date] | null>(null);

// 收款记录表格数据和列定义
const receiptRecords = ref<ReceiptRecord[]>([]);
// 保存原始记录数据的映射表
const originalRecordsMap = ref<Map<string, any>>(new Map());
const receiptLoading = ref(false);
const receiptPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
});

const receiptColumns: ColumnType<ReceiptRecord>[] = [
  {
    title: '收款单号',
    dataIndex: 'sapReceiveNo',
    width: '150px',
    align: 'center',
  },
  {
    title: '付款单位',
    dataIndex: 'merchantName',
    width: '150px',
    align: 'center',
  },
  {
    title: '付款时间',
    dataIndex: 'payTime',
    width: '150px',
    align: 'center',
    customRender: ({ text }) => {
      if (!text) return '-';
      
      // 检查是否已经是格式化后的日期字符串（YYYY-MM-DD）
      if (typeof text === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(text)) {
        return text;
      }
      
      // 如果格式为YYYYMMDD，转换为YYYY-MM-DD
      if (typeof text === 'string' && /^\d{8}$/.test(text)) {
        return `${text.slice(0, 4)}-${text.slice(4, 6)}-${text.slice(6, 8)}`;
      }
      
      try {
        // 判断是否为时间戳格式（数字）
        const timestamp = typeof text === 'number' ? text : parseInt(text);
        if (!isNaN(timestamp) && timestamp > 0) {
          const date = new Date(timestamp);
          if (!isNaN(date.getTime())) {
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
          }
        }
        
        // 尝试处理其他日期字符串格式
        const date = new Date(text);
        if (!isNaN(date.getTime())) {
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        }
        
        return text;
      } catch (e) {
        console.error('日期格式化错误:', e);
        return text;
      }
    }
  },
  {
    title: '付款金额',
    dataIndex: 'amount',
    width: '150px',
    align: 'center',
    customRender: ({ text }) => {
      return `${text} 元`;
    }
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '100px',
    align: 'center',
  },
];

// 监听收款时间范围变化
watch(() => receiptTimeRange.value, (newVal) => {
  if (newVal) {
    receiptSearchParam.value.payTimeStart = newVal[0];
    receiptSearchParam.value.payTimeEnd = newVal[1];
  } else {
    receiptSearchParam.value.payTimeStart = null;
    receiptSearchParam.value.payTimeEnd = null;
  }
});

// 监听弹窗开启，自动加载数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadReceiptRecords();
  }
});

// 搜索收款记录
const searchReceipts = () => {
  console.log('点击了搜索按钮');
  console.log('当前搜索参数：', JSON.stringify(receiptSearchParam.value));
  loadReceiptRecords();
};

// 加载收款记录数据
const loadReceiptRecords = async () => {
  receiptLoading.value = true;
  try {
    // 获取当前年份的1月1日
    const currentYear = new Date().getFullYear();
    const startDate = `${currentYear}-01-01`;
    // 获取今天的日期
    const today = formatDate(new Date());

    const params: any = {
      codes: [1120], // 默认code值
      startDate,
      endDate: today
    };

    if (receiptSearchParam.value.merchantName) {
      console.log('付款单位名称:', receiptSearchParam.value.merchantName);
      params.codes = [receiptSearchParam.value.merchantName];
    }

    // 如果用户选择了自定义时间范围，则使用用户选择的时间
    if (receiptSearchParam.value.payTimeStart && receiptSearchParam.value.payTimeEnd) {
      params.startDate = formatDate(new Date(receiptSearchParam.value.payTimeStart));
      params.endDate = formatDate(new Date(receiptSearchParam.value.payTimeEnd));
      console.log('自定义时间范围:', params.startDate, '到', params.endDate);
    }

    const res = await bondApi.getSapList(params);

    // 处理返回的数据，将其转换为ReceiptRecord类型
    const records = Array.isArray(res) ? res : (res?.records || []);

    // 清空原始记录映射
    originalRecordsMap.value.clear();

    receiptRecords.value = records.map((item: any) => {
      // 保存原始记录到映射表中
      originalRecordsMap.value.set(item.belnr || '', item);

      // 返回转换后的记录用于表格显示
      return {
        id: item.belnr || '',
        sapReceiveNo: item.belnr || '',
        merchantName: item.name1 || '',
        payTime: item.budat || '',
        amount: parseFloat(item.dmbtr || '0')
      } as ReceiptRecord;
    });
    receiptPagination.value.total = Array.isArray(res) ? res.length : (res?.total || 0);
  } catch (error) {
    console.error('获取收款记录失败:', error);
    message.error('获取收款记录失败，请重试');
  } finally {
    receiptLoading.value = false;
  }
};

// 处理收款记录表格分页变化
const handleReceiptTableChange = (pagination: any) => {
  receiptPagination.value.current = pagination.current;
  receiptPagination.value.pageSize = pagination.pageSize;
  loadReceiptRecords();
};

// 重置收款记录搜索条件
const resetReceiptSearch = () => {
  receiptSearchParam.value = {
    merchantName: '',
    payTimeStart: null,
    payTimeEnd: null,
    code: [] as string[]
  };
  receiptTimeRange.value = null;
};

// 选择收款记录
const selectReceipt = (record: ReceiptRecord) => {
  const originalRecord = originalRecordsMap.value.get(record.sapReceiveNo);

  if (originalRecord) {
    const completeData = {
      ...record,
      originalData: originalRecord
    };
    emit('select', completeData as any);
  } else {
    emit('select', record);
  }

  closeModal();
};

const closeModal = () => {
  emit('update:visible', false);
  emit('cancel');
};
</script>

<template>
  <a-modal :open="visible" title="选择收款记录" @cancel="closeModal" :footer="null" width="800px">
    <div>
      <h-row :align="'middle'" style="margin-bottom: 20px;">
        <h-col :span="4" style="text-align: right; padding-right: 10px">
          <label for="payerName">付款单位：</label>
        </h-col>
        <h-col :span="6">
          <h-input v-model:value="receiptSearchParam.merchantName" placeholder="请输入结算单位编码" allow-clear />
        </h-col>
        <h-col :span="4" style="text-align: right; padding-right: 10px">
          <label for="payTime">付款时间：</label>
        </h-col>
        <h-col :span="8">
          <h-range-picker
            v-model:value="receiptTimeRange"
            value-format="YYYY-MM-DD"
            style="width: 100%"
            allow-clear
            :getPopupContainer="(triggerNode: HTMLElement) => triggerNode.parentNode as HTMLElement"
            placement="bottomLeft"
            :dropdown-style="{ zIndex: 9999 }"
          />
        </h-col>
      </h-row>

      <h-row :align="'middle'" style="margin-bottom: 20px;">
        <h-col :span="24" style="text-align: right;">
          <h-button style="margin-right: 10px" @click="resetReceiptSearch">重置</h-button>
          <h-button type="primary" @click="searchReceipts">
            <SearchOutlined />查询
          </h-button>
        </h-col>
      </h-row>

      <h-table :columns="receiptColumns" :data-source="receiptRecords" :pagination="receiptPagination" :size="'small'"
        :loading="receiptLoading" @change="handleReceiptTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'operation'">
            <h-button type="link" @click="() => selectReceipt(record)">选择</h-button>
          </template>
        </template>
      </h-table>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
/* 如有需要添加特定样式 */
</style>