<script setup lang="ts">
// 方案互动-住宿方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits, defineExpose } from 'vue';
import { throttle } from 'lodash';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import {
  hotelLevelAllConstant,
  RoomTypeConstant,
  BreakfastTypeConstant,
  HotelsArr,
  schemeStaysArr,
} from '@haierbusiness-front/common-libs';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  demandHotels: {
    type: Array,
    default: [],
  },
  hotels: {
    type: Array,
    default: [],
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  processNode: {
    type: String,
    default: '',
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
  merchantType: {
    type: Number,
    default: null,
  },
  readonly: {
    // 是否为只读模式
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemeStaysEmit']);

const oldSchemeList = ref<Array<any>>([]);
const newSchemeList = ref<Array<any>>([]);

const hotelList = ref<Array<any>>([]);
const schemeDifferenceStays = ref<Array<any>>([]);
const accommodationDifferencesShow = ref(false);

// 住宿
const staysParams = ref<any>({
  tempId: undefined, //
  tempSchemeHotelId: undefined, //
  miceDemandHotelId: undefined, // 需求酒店id
  miceDemandStayId: undefined, // 需求住宿id
  miceSchemeHotelId: undefined, // 方案酒店id
  miceSchemeStayId: undefined, // 方案住宿id
  demandDate: {} as Record<string, unknown>, // 需求日期
  roomType: undefined, // 房型 大床/双床/套房
  breakfastType: 1, // 早餐类型 无早/单早/双早
  personNum: undefined, // 人数
  schemeRoomNum: undefined, // 方案入住房间数
  billRoomNum: undefined, // 账单入住房间数
  discrepancyReason: '', // 人数与房间数不一致原因
  description: '', // 方案说明

  schemeUnitPrice: undefined, // 方案单价
  billUnitPrice: undefined, // 账单单价
  agreementProductId: undefined, // 协议产品id
  agreementUnitPrice: undefined, // 协议单价, 来源于协议产品关联协议价格
  marketUnitPrice: undefined, // 市场单价, 来源于市场价比价功能
  retailUnitPrice: undefined, // 门市单价, 来源于协议产品关联门市价格
  msMarketPriceInquiryDetailsId: undefined, // 市场价询价单记录id
  sourceId: undefined, // 当前方案来源方案id,当当前方案为竞价方案时,此id是互动方案id,当方案为变更方案时,为上一版本id
  invoiceTempId: undefined, // 临时id, 用于关联发票表
  statementTempId: undefined, // 临时id, 用于关联水单表
});

// 住宿方案
const schemePlanLabelList = ['酒店选择', '房型', '人数', '房间数', '不一致原因', '备注'];
// 需求差异
const demandTable = ref<Array<any>>([]);
// 方案差异
const planTable = ref<Array<any>>([]);
const diffColor = ref<Array<boolean>>([]);
const isShowDiff = ref<boolean>(false);

const diffRemarks = ref<string>(''); // 差异原因
const subtotal = ref<number>(0); // 小计

const isVerifyFailed = ref<boolean>(false); // 校验是否失败

// 差异
watch(
  () => newSchemeList.value,
  (newObj) => {
    if (newObj?.length > 0) {
      let td1 = 0;
      let td2 = '';
      let td3 = 0;
      let td4 = 0;
      let td5 = 0;

      newObj.forEach((e) => {
        td1 += e.personNum || 0;

        td2 =
          (td2 ? td2 + '，' : '') +
          (RoomTypeConstant.ofType(e.roomType)?.desc || '-') +
          '/' +
          (BreakfastTypeConstant.ofType(e.breakfastType)?.desc || '-') +
          '*' +
          (e.schemeRoomNum || 0);

        if (e.roomType === 1) {
          // 大床房（默认1人）
          td3 += e.schemeRoomNum;
        }
        if (e.roomType === 2) {
          // 双床房
          td4 += e.schemeRoomNum;
        }
        if (e.roomType === 3) {
          // 套房（默认1人）
          td5 += e.schemeRoomNum;
        }
      });

      // 需求差异
      planTable.value = ['方案', td1, td2, td3, td4, td5];

      diffColor.value = planTable.value.map((e: any, i: number) => {
        return demandTable.value[i] === e;
      });
      diffColor.value[0] = true;

      isShowDiff.value = !diffColor.value.every((e: boolean) => e === true);

      // 价格计算
      priceCalcFun();
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

watch(
  () => [props.hotels, props.merchantType, newSchemeList.value],
  () => {
    hotelList.value = props.hotels ? [...props.hotels] : [];

    newSchemeList.value.forEach((e: any, index: number) => {
      if (props.hotels && props.hotels.length === 1) {
        // 是否直签酒店
        e.tempSchemeHotelId =
          props.merchantType !== 1
            ? (props.hotels[0] as any).tempId
            : e.miceSchemeHotelId || (props.hotels[0] as any).tempId;
        e.miceDemandPushHotelId = (props.hotels[0] as any).miceDemandPushHotelId;
      }
    });
  },
  {
    immediate: true,
    deep: true,
  },
);

// 酒店名称
const hotelName = (hotelItem: HotelsArr, itemIndex?: number) => {
  let str = '-';

  // 优先使用demandHotels中的真实酒店名称
  if (props.demandHotels && props.demandHotels.length > 0) {
    // 根据索引匹配对应的需求酒店
    if (itemIndex !== undefined && props.demandHotels[itemIndex]) {
      const demandHotel = props.demandHotels[itemIndex] as any;
      if (demandHotel.hotelName) {
        str = demandHotel.hotelName;
        return str;
      }
    }

    // 通过ID匹配需求酒店
    const demandHotel = (props.demandHotels as any[]).find(
      (e: any) =>
        e.id === (hotelItem as any).miceSchemeHotelId ||
        e.miceDemandPushHotelId === (hotelItem as any).miceDemandPushHotelId,
    );
    if (demandHotel && demandHotel.hotelName) {
      str = demandHotel.hotelName;
      return str;
    }
  }

  // 备用方案：根据住宿项的酒店ID来查找对应的酒店
  if (hotelItem && (hotelItem as any).tempSchemeHotelId && props.hotels && props.hotels.length > 0) {
    const hotelIndex = props.hotels.findIndex((hotel: any) => hotel.tempId === (hotelItem as any).tempSchemeHotelId);
    if (hotelIndex !== -1) {
      const targetHotel = props.hotels[hotelIndex] as any;
      str = targetHotel.hotelName || '-';
      return str;
    }
  }

  // 如果没有tempSchemeHotelId，但传入了索引且对应索引的酒店存在
  if (itemIndex !== undefined && props.hotels && props.hotels[itemIndex]) {
    const hotel = props.hotels[itemIndex] as any;
    str = hotel.hotelName || '-';
    return str;
  }

  // 兜底逻辑：显示城市区域信息
  (props.demandHotels as any[]).forEach((e: any, index: number) => {
    if (e.id && e.id === (hotelItem as any).miceSchemeHotelId) {
      // 优先显示酒店名称，如果没有则显示城市区域信息
      str = e.hotelName || `${e.cityName + e.districtName}/${hotelLevelAllConstant.ofType(e.level)?.desc || '-'}`;
    }
  });

  return str;
};

const changePrice = throttle((index: number) => {
  // 价格计算
  priceCalcFun();
}, 500);

// 价格计算
const priceCalcFun = () => {
  const isAllPriceWrite = newSchemeList.value.every(
    (e: any) => e.billRoomNum && e.billUnitPrice && e.billRoomNum >= 0 && e.billUnitPrice >= 0,
  );

  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e: any) => {
      subtotal.value += e.billRoomNum * e.billUnitPrice;
    });

    emit('schemePriceEmit', { type: 'stay', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  }
};

// 房间数
const changeRoomNum = (i: number) => {
  // 大床房（默认1人） - 1
  // 双床房 - 2
  // 套房（默认1人） - 3

  if (newSchemeList.value[i]?.roomType && newSchemeList.value[i]?.personNum && newSchemeList.value[i]?.billRoomNum) {
    if (
      Number(newSchemeList.value[i].personNum) ===
      Number(newSchemeList.value[i].billRoomNum) *
        (newSchemeList.value[i].roomType === 1 || newSchemeList.value[i].roomType === 3 ? 1 : 2)
    ) {
      // 人数、房间数匹配时，清空不一致原因
      accommodationDifferencesShow.value = false;
      newSchemeList.value[i].discrepancyReason = '';
    } else {
      accommodationDifferencesShow.value = true;
    }
  }

  // 触发价格计算
  priceCalcFun();
};

// 锚点
const anchorJump = (id: string) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};

// 暂存
const stayTempSave = () => {
  const emitData = {
    schemeStays: [...newSchemeList.value],
    schemeDifferenceStays: [...schemeDifferenceStays.value],
    schemeIndex: props.schemeIndex,
  };

  emit('schemeStaysEmit', emitData);
};

// 校验
const staySub = () => {
  let isStayVerPassed = true;

  newSchemeList.value.forEach((e: any, i: number) => {
    isVerifyFailed.value = true;

    if (isStayVerPassed === false) return;
    if (!e.schemeRoomNum) {
      message.error('请输入' + e.demandDate + '住宿' + (i + 1) + '房间数');

      isStayVerPassed = false;
      anchorJump('schemeStayId' + e.demandDate + i);
      return;
    }

    if (!e.billUnitPrice) {
      message.error('请输入' + e.demandDate + '住宿' + (i + 1) + '单价');

      isStayVerPassed = false;
      anchorJump('schemeStayId' + e.demandDate + i);
      return;
    }
    if (accommodationDifferencesShow.value && !e.discrepancyReason) {
      message.error('请输入' + e.demandDate + '住宿' + (i + 1) + '不一致原因');
      isStayVerPassed = false;
      anchorJump('schemeStayId' + e.demandDate + i);
      return;
    }
  });

  // 住宿差异
  let differenceDate =
    oldSchemeList.value[0]?.demandDate || newSchemeList.value[0]?.demandDate || ({} as Record<string, unknown>); // 差异日期
  let demandTotalPerson = 0; // 需求当日总人数
  let schemeTotalPerson = 0; // 方案当日总人数

  let demandRoomTypeList: any[] = []; // 需求房型统计
  let schemeRoomTypeList: any[] = []; // 方案房型统计
  let demandRoomType = ''; // 需求房型统计
  let schemeRoomType = ''; // 方案房型统计
  let demandOneRooms = 0; // 	需求大床数量
  let schemeOneRooms = 0; // 方案大床数量
  let demandTwoRooms = 0; // 需求双床数量
  let schemeTwoRooms = 0; // 方案双床数量
  let demandSuiteRooms = 0; // 需求套床数量
  let schemeSuiteRooms = 0; // 方案套床数量

  let reason = ''; // 差异原因
  let demandStayIdList: any[] = []; // 需求住宿
  let demandStayIds = ''; // 需求住宿

  // 需求

  oldSchemeList.value?.forEach((e: any) => {
    demandTotalPerson += e.personNum;
    demandRoomTypeList.push(e.roomType);

    demandStayIdList.push(e.id);

    if (e.roomType === 1) {
      // 大床房（默认1人）
      demandOneRooms += e.schemeRoomNum || 0;
    }
    if (e.roomType === 2) {
      // 双床房
      demandTwoRooms += e.schemeRoomNum || 0;
    }
    if (e.roomType === 3) {
      // 套房（默认1人）
      demandSuiteRooms += e.schemeRoomNum || 0;
    }
  });

  demandRoomTypeList = Array.from(new Set(demandRoomTypeList));
  demandRoomType = demandRoomTypeList.join(',');

  demandStayIdList = Array.from(new Set(demandStayIdList));
  demandStayIds = demandStayIdList.join(',');

  // 方案
  newSchemeList.value?.forEach((e: any) => {
    schemeTotalPerson += e.personNum;
    schemeRoomTypeList.push(e.roomType);

    if (e.roomType === 1) {
      // 大床房（默认1人）
      schemeOneRooms += e.schemeRoomNum;
    }
    if (e.roomType === 2) {
      // 双床房
      schemeTwoRooms += e.schemeRoomNum;
    }
    if (e.roomType === 3) {
      // 套房（默认1人）
      schemeSuiteRooms += e.schemeRoomNum;
    }
  });

  schemeRoomTypeList = Array.from(new Set(schemeRoomTypeList));
  schemeRoomType = schemeRoomTypeList.join(',');

  const tempIdList = newSchemeList.value.filter((e: any) => e.tempId);
  const idsList = tempIdList.map((e: any) => {
    return e.tempId;
  });
  const schemeTempStayIds = idsList.join(',');

  schemeDifferenceStays.value = [];

  if (
    demandTotalPerson !== schemeTotalPerson ||
    demandOneRooms !== schemeOneRooms ||
    demandTwoRooms !== schemeTwoRooms ||
    demandSuiteRooms !== schemeSuiteRooms
  ) {
    schemeDifferenceStays.value = [
      {
        schemeTempStayIds: schemeTempStayIds,
        demandStayIds: demandStayIds,
        differenceDate: differenceDate,

        demandTotalPerson: demandTotalPerson,
        schemeTotalPerson: schemeTotalPerson,
        demandRoomType: demandRoomType,
        schemeRoomType: schemeRoomType,

        demandOneRooms: demandOneRooms || 0,
        schemeOneRooms: schemeOneRooms || 0,
        demandTwoRooms: demandTwoRooms || 0,
        schemeTwoRooms: schemeTwoRooms || 0,
        demandSuiteRooms: demandSuiteRooms || 0,
        schemeSuiteRooms: schemeSuiteRooms || 0,
        reason: diffRemarks.value,
      },
    ];
  }

  if (schemeDifferenceStays.value.length > 0 && !diffRemarks.value && isStayVerPassed) {
    message.error('请选择' + differenceDate + '住宿差异原因');

    isStayVerPassed = false;
    anchorJump('schemeStayDiffId' + newSchemeList.value[0].demandDate);
  }

  if (isStayVerPassed) {
    stayTempSave();
  }

  return isStayVerPassed;
};

defineExpose({ staySub, stayTempSave });

onMounted(async () => {
  if (props.schemeItem && props.schemeItem.stays) {
    oldSchemeList.value = JSON.parse(JSON.stringify(props.schemeItem))?.stays || [];

    if (oldSchemeList.value && oldSchemeList.value.length > 0) {
      if (
        props.isSchemeCache &&
        props.schemeCacheItem &&
        props.schemeCacheItem.stays &&
        props.schemeCacheItem.stays.length > 0 &&
        !props.readonly // 查看模式下不使用缓存
      ) {
        newSchemeList.value =
          props.schemeCacheItem?.stays.map((cacheItem: any, idx: number) => {
            // 从原始数据中获取对应的项目
            const originalItem = oldSchemeList.value[idx];

            // 使用缓存数据，但关键ID字段和只读字段使用原始数据
            return {
              ...cacheItem,
              // 关键ID字段使用原始数据，确保数据一致性
              miceSchemeHotelId: originalItem?.miceSchemeHotelId || cacheItem.miceSchemeHotelId,
              miceDemandHotelId: originalItem?.miceDemandHotelId || cacheItem.miceDemandHotelId,
              miceDemandStayId: originalItem?.miceDemandStayId || cacheItem.miceDemandStayId,
              miceSchemeStayId: originalItem?.id || cacheItem.miceSchemeStayId, // 使用详情中的id作为方案住宿id
              // 只读字段使用原始数据，不允许被缓存覆盖
              schemeRoomNum: originalItem?.schemeRoomNum || cacheItem.schemeRoomNum,
              schemeUnitPrice: originalItem?.schemeUnitPrice || cacheItem.schemeUnitPrice,
              personNum: originalItem?.personNum || cacheItem.personNum,
              roomType: originalItem?.roomType || cacheItem.roomType,
              breakfastType: originalItem?.breakfastType || cacheItem.breakfastType,
              demandDate: originalItem?.demandDate || cacheItem.demandDate,
              discrepancyReason: originalItem?.discrepancyReason || cacheItem.discrepancyReason,
              description: originalItem?.description || cacheItem.description,
              sourceId: originalItem?.sourceId || cacheItem.sourceId,
              billRoomNum: originalItem?.billRoomNum || cacheItem.billRoomNum || originalItem?.schemeRoomNum,
              billUnitPrice: originalItem?.billUnitPrice || cacheItem.billUnitPrice,
            };
          }) || [];

        // 差异原因
        diffRemarks.value = props.schemeCacheItem?.differenceStays[0]?.reason;

        // 价格计算
        priceCalcFun();
      } else {
        // 从需求数据生成方案数据，确保右侧有数据显示
        newSchemeList.value = oldSchemeList.value.map((e: any, idx: number) => {
          // 优先使用 hotelList.value，如果为空则使用 props.hotels 作为兜底
          const availableHotels = hotelList.value.length > 0 ? hotelList.value : props.hotels || [];
          // 根据索引自动关联对应的酒店，如果酒店数量不足则关联第一个酒店
          const targetHotelIndex = idx < availableHotels.length ? idx : 0;
          const targetHotel = availableHotels.length > 0 ? availableHotels[targetHotelIndex] : null;

          const newItem = {
            tempId: Date.now() + idx,
            miceDemandHotelId: e.miceDemandHotelId,
            miceSchemeHotelId: e.miceSchemeHotelId, // 保持和左边一样的酒店ID字段
            miceSchemeStayId: e.id, // 方案住宿id - 使用详情中的id
            tempSchemeHotelId: targetHotel ? (targetHotel as any).tempId : undefined,
            miceDemandPushHotelId: targetHotel ? (targetHotel as any).miceDemandPushHotelId : undefined,
            miceDemandStayId: e.miceDemandStayId,
            demandDate: e.demandDate,
            roomType: e.roomType,
            breakfastType: e.breakfastType,
            personNum: e.personNum,
            schemeRoomNum: e.schemeRoomNum,
            // 查看模式使用详情数据，编辑模式使用默认值
            billRoomNum: props.readonly ? e.billRoomNum || e.schemeRoomNum : e.schemeRoomNum,
            discrepancyReason: e.discrepancyReason,
            description: e.description,
            schemeUnitPrice: e.schemeUnitPrice,
            // 查看模式使用详情数据，编辑模式使用默认值
            billUnitPrice: props.readonly ? e.billUnitPrice || null : null,
            agreementProductId: e.agreementProductId,
            agreementUnitPrice: e.agreementUnitPrice,
            marketUnitPrice: e.marketUnitPrice,
            retailUnitPrice: e.retailUnitPrice,
            msMarketPriceInquiryDetailsId: e.msMarketPriceInquiryDetailsId,
            sourceId: e.sourceId,
            // 查看模式使用详情数据，编辑模式使用默认值
            invoiceTempId: props.readonly ? e.invoiceTempId || undefined : undefined,
            statementTempId: props.readonly ? e.statementTempId || undefined : undefined,
          };

          return newItem;
        });
      }
    } else {
      // 如果没有需求数据，但有缓存数据，也要显示
      if (
        props.isSchemeCache &&
        props.schemeCacheItem &&
        props.schemeCacheItem.stays &&
        props.schemeCacheItem.stays.length > 0
      ) {
        newSchemeList.value = props.schemeCacheItem?.stays || [];
        diffRemarks.value = props.schemeCacheItem?.differenceStays[0]?.reason;
        priceCalcFun();
      }
    }

    if (oldSchemeList.value?.length > 0) {
      let td1 = 0;
      let td2 = '';
      let td3 = 0;
      let td4 = 0;
      let td5 = 0;

      oldSchemeList.value.forEach((e: any) => {
        td1 += e.personNum || 0;

        td2 =
          (td2 ? td2 + '，' : '') +
          (RoomTypeConstant.ofType(e.roomType)?.desc || '-') +
          '/' +
          (BreakfastTypeConstant.ofType(e.breakfastType)?.desc || '-') +
          '*' +
          (e.schemeRoomNum || 0);

        if (e.roomType === 1) {
          // 大床房（默认1人）
          td3 += e.schemeRoomNum;
        }
        if (e.roomType === 2) {
          // 双床房
          td4 += e.schemeRoomNum;
        }
        if (e.roomType === 3) {
          // 套房（默认1人）
          td5 += e.schemeRoomNum;
        }
      });

      // 需求差异
      demandTable.value = ['需求', td1, td2, td3, td4, td5];
    }
  }

  stayTempSave();
});
</script>

<template>
  <!-- 住宿方案 -->
  <div class="scheme_stay">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l" v-if="props.schemeType !== 'notBidding' && props.schemeType !== 'biddingView'">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>住宿方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '住宿' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ hotelName(item, idx) }}
                </template>
                {{ hotelName(item, idx) }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{
                    (RoomTypeConstant.ofType(item.roomType)?.desc || '-') +
                    '/' +
                    (BreakfastTypeConstant.ofType(item.breakfastType)?.desc || '-')
                  }}
                </template>
                {{
                  (RoomTypeConstant.ofType(item.roomType)?.desc || '-') +
                  '/' +
                  (BreakfastTypeConstant.ofType(item.breakfastType)?.desc || '-')
                }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.personNum ? item.personNum + '人' : '-' }}
                </template>
                {{ item.personNum ? item.personNum + '人' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeRoomNum + '间夜' }}
                </template>
                {{ item.schemeRoomNum + '间夜' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.discrepancyReason || '-' }}
                </template>
                {{ item.discrepancyReason || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">{{ item.description || '-' }}</div>
          </div>
          <!-- 左侧只读单价和总额 -->
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div class="scheme_plan_price_value readonly">
                {{ item.schemeUnitPrice ? '¥' + formatNumberThousands(item.schemeUnitPrice) : '¥0.00' }}
              </div>
            </div>

            <div class="scheme_plan_price mt20">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value readonly">
                {{
                  '¥' +
                  (item.schemeRoomNum && item.schemeUnitPrice
                    ? formatNumberThousands(item.schemeRoomNum * item.schemeUnitPrice)
                    : '0.00')
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <span v-if="item.schemeRoomNum && item.schemeUnitPrice">
                {{ item.schemeRoomNum + '(间夜)*' + item.schemeUnitPrice + '(单价)' }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div :class="['common_table_r', 'pr30']">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>住宿账单</span>
        </div>

        <div
          class="scheme_plan_table mt20"
          v-for="(item, idx) in newSchemeList"
          :key="idx"
          :id="'schemeStayId' + item.demandDate + idx"
        >
          <div class="scheme_plan_index">
            {{ '住宿' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value">
              <div class="scheme_plan_border pr0">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ hotelName(item, idx) }}
                  </template>
                  {{ hotelName(item, idx) }}
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border pr0">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{
                      (RoomTypeConstant.ofType(item.roomType)?.desc || '-') +
                      '/' +
                      (BreakfastTypeConstant.ofType(item.breakfastType)?.desc || '-')
                    }}
                  </template>
                  {{
                    (RoomTypeConstant.ofType(item.roomType)?.desc || '-') +
                    '/' +
                    (BreakfastTypeConstant.ofType(item.breakfastType)?.desc || '-')
                  }}
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border pr0">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.personNum ? item.personNum + '人' : '-' }}
                  </template>
                  {{ item.personNum ? item.personNum + '人' : '-' }}
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value pl12" v-if="props.readonly">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeRoomNum + '间夜' }}
                </template>
                {{ item.schemeRoomNum + '间夜' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value" v-else style="padding-left: 3px">
              <!-- 编辑模式：显示输入框 -->
              <div>
                <div>
                  <a-input-number
                    style="width: calc(100% - 58px)"
                    v-model:value="item.billRoomNum"
                    @change="changeRoomNum(idx)"
                    placeholder="房间数"
                    :bordered="false"
                    allow-clear
                    :min="1"
                    :max="99999"
                    :precision="0"
                  />
                  <span>间夜</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value" v-if="!accommodationDifferencesShow">
              <div class="scheme_plan_border pr0">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.discrepancyReason || '-' }}
                  </template>
                  {{ item.discrepancyReason || '-' }}
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value" v-if="accommodationDifferencesShow">
              <div :class="['scheme_plan_border', 'pr0', isVerifyFailed && !item.discrepancyReason ? 'error_tip' : '']">
                <a-tooltip placement="topLeft" :title="item.discrepancyReason || '-'">
                  <a-input
                    style="width: 95%"
                    v-model:value="item.discrepancyReason"
                    placeholder="不一致原因"
                    :bordered="false"
                    allow-clear
                    :maxlength="200"
                  />
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.description || '-' }}
                  </template>
                  {{ item.description || '-' }}
                </a-tooltip>
              </div>
            </div>
          </div>

          <!-- 账单单价和账单实际数量 -->
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div class="scheme_plan_price_value readonly" v-if="props.readonly">
                {{ item.billUnitPrice ? '¥' + formatNumberThousands(item.billUnitPrice) : '¥0.00' }}
              </div>
              <div
                :class="['scheme_plan_price_value', isVerifyFailed && !item.billUnitPrice ? 'error_price_tip' : '']"
                v-else
              >
                <a-input-number
                  v-model:value="item.billUnitPrice"
                  @change="changePrice(idx)"
                  placeholder=""
                  :bordered="false"
                  :controls="false"
                  :min="0.01"
                  :max="999999.99"
                  :precision="2"
                  style="width: 100%"
                  allow-clear
                />
              </div>
            </div>

            <div class="scheme_plan_price mt20">
              <div class="scheme_plan_price_label">总额：</div>
              <div :class="['scheme_plan_price_value', props.readonly ? 'readonly' : '']">
                {{
                  '¥' +
                  (item.billRoomNum && item.billUnitPrice
                    ? formatNumberThousands(item.billRoomNum * item.billUnitPrice)
                    : '0.00')
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <span v-if="item.billRoomNum && item.billUnitPrice">
                {{ item.billRoomNum + '(间夜)*' + item.billUnitPrice + '(单价)' }}
              </span>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_stay {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_stay.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  :deep(.ant-select) {
    /* 下拉框 */
    width: 100%;
    height: 100%;
  }

  :deep(.ant-select-selector) {
    padding: 0;
  }

  :deep(.ant-select-arrow) {
    /* 下拉箭头 */
    margin-top: -8px;
  }

  :deep(.ant-select .ant-select-clear) {
    /* 清除图标 */
    margin-top: -8px;
  }

  :deep(.ant-input-affix-wrapper) {
    padding-left: 0;
    padding-right: 0;
  }

  .scheme_plan_value {
    /* 住宿人数和房间数 */
    :deep(.ant-input-number .ant-input-number-input) {
      height: 22px;
      padding: 0 px;
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .border_err {
    border-color: red !important;
  }

  .error_tip {
    border: 2px solid #ff4d4f !important;
  }

  .scheme_plan_price_value {
    /* 竞价单价 */
    :deep(.ant-input-number .ant-input-number-input) {
      /* input数字输入框 */
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }

    /* 只读模式样式 */
    &.readonly {
      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      height: 24px;
      line-height: 24px;
      padding: 0 5px;
    }
  }

  .error_price_tip {
    :deep(.ant-input-number .ant-input-number-input) {
      border-bottom: 2px solid #ff4d4f !important;
    }
  }

  .add_scheme_plan {
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;

    color: #4e5969;
    background: #ffffff;
    border-radius: 2px;
    border: 1px dashed #e5e6eb;
    cursor: pointer;
    user-select: none;

    &:hover {
      color: #1868db;
      border-color: #1868db;

      .plan_add_img {
        width: 16px;
        height: 16px;
        background: url('@/assets/image/demand/demand_add_blue.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }

    .plan_add_img {
      width: 16px;
      height: 16px;
      background: url('@/assets/image/demand/add_white.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }

  .scheme_plan_list3 {
    position: relative;

    .action_icons {
      position: absolute;
      right: -24px;
      top: 50%;
      transform: translateY(-50%);

      .del_icon {
        width: 16px;
        height: 40px;

        cursor: pointer;
        background: url('@/assets/image/common/del_red.png');
        background-repeat: no-repeat;
        background-size: 16px 16px;
        background-position: center;
      }
    }
  }

  .pr0 {
    padding-right: 0 !important;
  }

  .pr30 {
    padding-right: 30px !important;
  }

  .scheme_stay_difference {
    height: 202px;
    background: #ffffff;
    border: 1px solid #e5e6eb;
    border-top: none;

    .stay_difference_border {
      padding: 16px 24px;
      width: 100%;
      height: 100%;
      border: 8px solid rgba(255, 85, 51, 0.2);
    }

    .stay_difference_title {
      font-weight: 500;
      font-size: 18px;
      color: #4e5969;
      line-height: 25px;
    }

    .stay_difference_content {
      .stay_difference_table {
        .stay_difference_tr {
          border-bottom: 1px solid #e5e6eb;
          display: flex;
          justify-content: space-between;
        }

        .stay_difference_td {
          width: 90px;
          height: 44px;
          color: #86909c;
          line-height: 44px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;

          &:first-child {
            width: 80px;
          }

          &:nth-child(3) {
            min-width: 160px;
            width: calc(100% - 440px - 30px);
          }
        }

        .diff_color {
          color: #ff5533;
        }
      }

      .stay_difference_reason {
        .stay_difference_reason_title {
          height: 44px;
          color: #4e5969;
          line-height: 44px;
        }

        .stay_difference_reason_text {
          :deep(.ant-input) {
            height: 88px !important;
            min-height: 88px !important;
            max-height: 88px !important;
          }
        }
      }
    }
  }

  /* custom-antd.css */
  :deep(.ant-select-disabled .ant-select-selector) {
    color: rgba(134, 144, 156, 1);
  }
}
</style>
